<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.10</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.chinamobile.sparrow</groupId>
    <artifactId>sparrow-ai</artifactId>
    <version>1.0.0.RELEASE</version>
    <name>sparrow-ai</name>

    <properties>
        <java.version>1.8</java.version>
        <agentsflex.version>1.2.2</agentsflex.version>
        <sparrow.version>1.1.1.0</sparrow.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.chinamobile.sparrow</groupId>
            <artifactId>sparrow-domain</artifactId>
            <version>${sparrow.version}</version>
        </dependency>

        <dependency>
            <groupId>com.agentsflex</groupId>
            <artifactId>agents-flex-bom</artifactId>
            <version>${agentsflex.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
        </dependency>
    </dependencies>

</project>