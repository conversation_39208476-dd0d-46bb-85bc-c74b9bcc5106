package com.chinamobile.sparrow.ai.infra.llm.plugin;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.service.search.IWebSearchService;
import org.apache.commons.lang3.tuple.Pair;

import java.util.ArrayList;
import java.util.List;

public class WebSearchPlugin implements IPlugin {

    public static final String PLUGIN_ID = "search";

    final int searchCount;
    final IWebSearchService webSearchService;

    public WebSearchPlugin(int searchCount, IWebSearchService webSearchService) {
        this.searchCount = searchCount;
        this.webSearchService = webSearchService;
    }

    @Override
    public String getId() {
        return PLUGIN_ID;
    }

    @Override
    public Pair<String, List<?>> invoke(String conversationId, String datasetId, String query, String userId) {
        Result<List<IWebSearchService.Page>> _pages = webSearchService.search(query, searchCount);
        if (!_pages.isOK() || _pages.data.isEmpty()) {
            return Pair.of(null, new ArrayList<>());
        }

        StringBuilder _prompt = new StringBuilder("以下是与用户问题相关的网络搜索结果：\n");

        for (int i = 0; i < _pages.data.size(); ++i) {
            IWebSearchService.Page _page = _pages.data.get(i);
            _prompt.append(String.format("%d. 标题: %s\n", i + 1, _page.getTitle()));
            _prompt.append(String.format("   链接: %s\n", _page.getUrl()));
            _prompt.append(String.format("   摘要: %s\n\n", _page.getSnippet()));
        }

        return Pair.of(_prompt.toString(), _pages.data);
    }

}