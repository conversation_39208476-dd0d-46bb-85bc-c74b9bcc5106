package com.chinamobile.sparrow.ai.infra.llm.memory;

import com.agentsflex.core.message.AiMessage;
import com.agentsflex.core.message.HumanMessage;
import com.agentsflex.core.message.Message;
import com.agentsflex.core.prompt.ImagePrompt;
import com.chinamobile.sparrow.ai.model.llm.Memory;
import com.chinamobile.sparrow.ai.repository.llm.AbstractMemoryRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.model.media.Media;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import com.chinamobile.sparrow.domain.util.IdWorker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class RedisChatMemory implements ChatMemory {

    protected final String id;
    protected final String keyPrefix;
    protected final Integer memorySize;

    protected final AbstractMemoryRepository memoryRepository;
    protected final AbstractMediaRepository mediaRepository;
    protected final RedisTemplate<String, Object> redisTemplate;
    protected final Logger logger;

    public RedisChatMemory(String keyPrefix, Integer memorySize, AbstractMemoryRepository memoryRepository, AbstractMediaRepository mediaRepository, RedisTemplate<String, Object> redisTemplate) {
        this(String.valueOf(IdWorker.getInstance().nextId()), keyPrefix, memorySize, memoryRepository, mediaRepository, redisTemplate);
    }

    public RedisChatMemory(String id, String keyPrefix, Integer memorySize, AbstractMemoryRepository memoryRepository, AbstractMediaRepository mediaRepository, RedisTemplate<String, Object> redisTemplate) {
        this.id = id;
        this.keyPrefix = keyPrefix;
        this.memorySize = memorySize;
        this.memoryRepository = memoryRepository;
        this.mediaRepository = mediaRepository;
        this.redisTemplate = redisTemplate;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public Object id() {
        return id;
    }

    @Override
    public void addMessage(Message message) {
        redisTemplate.opsForList().rightPush(key(id), message);
    }

    @Override
    public List<Message> getMessages() {
        return getMessages(0);
    }

    @Override
    public List<Message> getMessages(int count) {
        String _key = key(id);

        Long _size = redisTemplate.opsForList().size(_key);
        // 从数据库载入
        if (_size == null || _size == 0) {
            List<Memory> _memories = memoryRepository.find(count, id);
            memoryRepository.parseMessages(_memories).forEach(i -> addMessage((Message) i));
        }

        List<Object> _messages = redisTemplate.opsForList().range(_key, 0, count - 1);
        if (CollectionUtils.isEmpty(_messages)) {
            return new ArrayList<>();
        }

        // 获取文件
        List<String> _mediaIds = _messages.stream()
                .filter(i -> i instanceof ImagePrompt.TextAndImageMessage)
                .map(i -> ((ImagePrompt.TextAndImageMessage) i).getPrompt().getImageUrl())
                .collect(Collectors.toList());
        List<Media> _medias = _mediaIds.isEmpty() ? new ArrayList<>() : mediaRepository.find(_mediaIds, null);

        _messages = _messages.stream()
                .map(i -> {
                    if (i instanceof ImagePrompt.TextAndImageMessage) {
                        ImagePrompt.TextAndImageMessage _temp = (ImagePrompt.TextAndImageMessage) i;
                        _temp.setContent("");

                        Media _media = _medias.stream()
                                .filter(j -> Objects.equals(j.getId(), _temp.getPrompt().getImageUrl()))
                                .findFirst()
                                .orElse(null);
                        if (_media != null) {
                            try {
                                Result<String> _base64 = mediaRepository.getBase64String(_media);
                                if (_base64.isOK()) {
                                    _temp.getPrompt().setImageBase64(String.format("data:image/%s;base64,%s", _media.getExtension(), _base64.data));
                                }
                            } catch (Exception e) {
                                logger.error("从缓存读取多模态消息[{}]失败", ((ImagePrompt.TextAndImageMessage) i).getMessageContent(), e);
                            }
                        }

                        return _temp;
                    } else if (i instanceof HumanMessage) {
                        return (HumanMessage) i;
                    } else if (i instanceof AiMessage) {
                        AiMessage _temp = (AiMessage) i;
                        if (!StringUtils.hasLength(_temp.getContent())) {
                            _temp.setContent(_temp.getMessageContent() == null ? "" : _temp.getMessageContent().toString());
                        }

                        return _temp;
                    } else {
                        return null;
                    }
                })
                .collect(Collectors.toList());

        List<Message> _temp = new ArrayList<>();
        boolean _human = true;
        if (!_messages.isEmpty()) {
            _temp.add((Message) _messages.get(0));
            if (_messages.get(0) instanceof AiMessage) {
                _human = false;
            }
        }
        for (int i = 1; i < _messages.size(); i++) {
            if (_human && _messages.get(i) instanceof AiMessage) {
                _temp.add((Message) _messages.get(i));
                _human = false;
            } else if (!_human && (_messages.get(i) instanceof HumanMessage || _messages.get(i) instanceof ImagePrompt.TextAndImageMessage)) {
                _temp.add((Message) _messages.get(i));
                _human = true;
            }
        }

        return _temp;
    }

    @Override
    public void clear() {
        redisTemplate.delete(key(id));
    }

    protected String key(String id) {
        return keyPrefix + id;
    }

}