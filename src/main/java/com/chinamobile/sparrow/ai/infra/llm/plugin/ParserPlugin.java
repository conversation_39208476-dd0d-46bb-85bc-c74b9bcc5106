package com.chinamobile.sparrow.ai.infra.llm.plugin;

import com.chinamobile.sparrow.ai.model.llm.Conversation;
import com.chinamobile.sparrow.ai.model.llm.ConversationDocument;
import com.chinamobile.sparrow.ai.repository.llm.ConversationRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import com.chinamobile.sparrow.domain.service.DocumentReader;
import com.chinamobile.sparrow.domain.service.ICacheService;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

public class ParserPlugin implements IPlugin {

    public static final String PLUGIN_ID = "parser";

    final String cacheKeyTemplate;
    final Duration expiresIn; // 缓存2小时

    final ConversationRepository<? extends Conversation> conversationRepository;
    final AbstractMediaRepository mediaRepository;
    final ICacheService cacheService;
    final Logger logger;

    public ParserPlugin(String cacheKeyTemplate, Duration expiresIn, ConversationRepository<? extends Conversation> conversationRepository, AbstractMediaRepository mediaRepository, ICacheService cacheService) {
        this.cacheKeyTemplate = cacheKeyTemplate;
        this.expiresIn = expiresIn;
        this.conversationRepository = conversationRepository;
        this.mediaRepository = mediaRepository;
        this.cacheService = cacheService;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public String getId() {
        return PLUGIN_ID;
    }

    @Override
    public Pair<String, List<?>> invoke(String conversationId, String datasetId, String query, String userId) {
        List<Document> _documents = new ArrayList<>();

        if (!StringUtils.hasLength(conversationId)) {
            return Pair.of(null, _documents);
        }

        for (ConversationDocument i : conversationRepository.findDocuments(conversationId, false, userId)) {
            try {
                String _key = String.format(cacheKeyTemplate, i.getId());

                String _text = cacheService.get(_key, String.class);
                if (_text == null) {
                    Result<byte[]> _bytes = mediaRepository.getBytes(i.getDocument());
                    if (_bytes.isOK()) {
                        _text = DocumentReader.parse(i.getDocument().getName(), _bytes.data);

                        // 缓存
                        cacheService.set(_key, _text, expiresIn);
                    }
                }

                _documents.add(new Document(i.getId(), i.getDocument().getName(), _text));
            } catch (IOException e) {
                logger.error("解析文件[{}]失败", i.getId(), e);
            }
        }

        StringBuilder _prompt = new StringBuilder();
        if (!_documents.isEmpty()) {
            _prompt.append("以下是用户提供的文档：\n");

            for (int i = 0; i < _documents.size(); ++i) {
                _prompt.append(String.format("%d. 文档ID: %s\n", i + 1, _documents.get(i).getId()));
                _prompt.append(String.format("   文档名称: %s\n", _documents.get(i).getName()));
                _prompt.append(String.format("   内容: %s\n", _documents.get(i).getContent()));
            }
        }

        return Pair.of(_prompt.toString(), _documents);
    }

    public static class Document {

        String id;
        String name;
        String content;

        public Document(String id, String name, String content) {
            this.id = id;
            this.name = name;
            this.content = content;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

    }

}