package com.chinamobile.sparrow.ai.infra.llm.plugin;

import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.CollectionUtils;

import java.util.List;

public interface IPlugin {

    String getId();

    default boolean support(List<String> plugins) {
        return !CollectionUtils.isEmpty(plugins) && plugins.contains(getId());
    }

    Pair<String, List<?>> invoke(String conversationId, String datasetId, String query, String userId);

}