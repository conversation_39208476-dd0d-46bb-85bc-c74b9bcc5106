package com.chinamobile.sparrow.ai.infra.llm.embedding;

import com.agentsflex.core.document.Document;
import com.agentsflex.core.llm.embedding.EmbeddingModel;
import com.agentsflex.core.llm.embedding.EmbeddingOptions;
import com.agentsflex.core.store.VectorData;
import com.alibaba.fastjson.JSONPath;
import com.google.gson.JsonObject;
import okhttp3.*;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;

import java.io.IOException;

public class DefaultEmbeddingModel implements EmbeddingModel {

    protected final String baseUrl;
    protected final String apiKey;
    protected final String model;
    protected final okhttp3.OkHttpClient client;

    public DefaultEmbeddingModel(String baseUrl, String apiKey, String model, ConnectionPool connectionPool) {
        this.baseUrl = baseUrl;
        this.apiKey = apiKey;
        this.model = model;
        this.client = new OkHttpClient.Builder().connectionPool(connectionPool).build();
    }

    @Override
    public VectorData embed(Document document) {
        return embed(document, EmbeddingOptions.DEFAULT);
    }

    @Override
    public VectorData embed(Document document, EmbeddingOptions options) {
        JsonObject _json = new JsonObject();
        _json.addProperty("model", options.getModel());
        _json.addProperty("input", document.getContent());
        _json.addProperty("encoding_format", "float");
        RequestBody _body = RequestBody.create(_json.toString(), okhttp3.MediaType.get(org.springframework.http.MediaType.APPLICATION_JSON_VALUE));

        Request _request = new Request.Builder()
                .url(url())
                .addHeader(HttpHeaders.AUTHORIZATION, String.format("Bearer %s", apiKey))
                .post(_body)
                .build();

        try (Response response = client.newCall(_request).execute()) {
            switch (HttpStatus.valueOf(response.code())) {
                case OK:
                    VectorData _vector = new VectorData();
                    _vector.setVector(response.body() == null ? new double[]{} : JSONPath.read(response.body().string(), "$.data[0].embedding", double[].class));
                    return _vector;
                case BAD_REQUEST:
                case TOO_MANY_REQUESTS:
                case SERVICE_UNAVAILABLE:
                case GATEWAY_TIMEOUT:
                    String _message = JSONPath.read(response.body() == null ? response.message() : response.body().string(), "$.message", String.class);
                    throw new RuntimeException(_message);
                default:
                    throw new RuntimeException(response.message());
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    protected String url() {
        return baseUrl + "/v1/embeddings";
    }

}