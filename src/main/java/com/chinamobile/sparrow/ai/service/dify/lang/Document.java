package com.chinamobile.sparrow.ai.service.dify.lang;

import com.google.gson.annotations.SerializedName;

public class Document {

    String id;
    String name;
    int position;

    @SerializedName(value = "data_source_type")
    String dataSourceType;

    @SerializedName(value = "data_source_info")
    String dataSourceInfo;

    @SerializedName(value = "dataset_process_rule_id")
    String datasetProcessRuleId;

    @SerializedName(value = "created_from")
    String createdFrom;

    @SerializedName(value = "created_by")
    String createdBy;

    @SerializedName("created_at")
    String createdAt;

    int tokens;

    @SerializedName(value = "indexing_status")
    String indexingStatus;

    String error;

    boolean enabled;

    @SerializedName(value = "disabled_by")
    String disabledBy;

    @SerializedName(value = "disabled_at")
    String disabledAt;

    boolean archived;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getPosition() {
        return position;
    }

    public void setPosition(int position) {
        this.position = position;
    }

    public String getDataSourceType() {
        return dataSourceType;
    }

    public void setDataSourceType(String dataSourceType) {
        this.dataSourceType = dataSourceType;
    }

    public String getDataSourceInfo() {
        return dataSourceInfo;
    }

    public void setDataSourceInfo(String dataSourceInfo) {
        this.dataSourceInfo = dataSourceInfo;
    }

    public String getDatasetProcessRuleId() {
        return datasetProcessRuleId;
    }

    public void setDatasetProcessRuleId(String datasetProcessRuleId) {
        this.datasetProcessRuleId = datasetProcessRuleId;
    }

    public String getCreatedFrom() {
        return createdFrom;
    }

    public void setCreatedFrom(String createdFrom) {
        this.createdFrom = createdFrom;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    public int getTokens() {
        return tokens;
    }

    public void setTokens(int tokens) {
        this.tokens = tokens;
    }

    public String getIndexingStatus() {
        return indexingStatus;
    }

    public void setIndexingStatus(String indexingStatus) {
        this.indexingStatus = indexingStatus;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getDisabledBy() {
        return disabledBy;
    }

    public void setDisabledBy(String disabledBy) {
        this.disabledBy = disabledBy;
    }

    public String getDisabledAt() {
        return disabledAt;
    }

    public void setDisabledAt(String disabledAt) {
        this.disabledAt = disabledAt;
    }

    public boolean isArchived() {
        return archived;
    }

    public void setArchived(boolean archived) {
        this.archived = archived;
    }

}