package com.chinamobile.sparrow.ai.service.dify.lang;

import com.google.gson.annotations.SerializedName;

public class RetrieveMode {

    @SerializedName(value = "search_method")
    ENUM_SEARCH_METHOD searchMethod;

    @SerializedName(value = "reranking_enable")
    boolean rerankingEnable;

    @SerializedName(value = "reranking_mode")
    RerankingMode rerankingMode;

    float weights;

    @SerializedName(value = "top_k")
    int topK;

    @SerializedName(value = "score_threshold_enabled")
    boolean scoreThresholdEnabled;

    @SerializedName(value = "score_threshold")
    float scoreThreshold;

    public static enum ENUM_SEARCH_METHOD {
        keyword_search, semantic_search, full_text_search, hybrid_search;
    }

    public static class RerankingMode {

        @SerializedName(value = "reranking_provider_name")
        String rerankingProviderName;

        @SerializedName(value = "reranking_model_name")
        String rerankingModelName;

        public String getRerankingProviderName() {
            return rerankingProviderName;
        }

        public void setRerankingProviderName(String rerankingProviderName) {
            this.rerankingProviderName = rerankingProviderName;
        }

        public String getRerankingModelName() {
            return rerankingModelName;
        }

        public void setRerankingModelName(String rerankingModelName) {
            this.rerankingModelName = rerankingModelName;
        }

    }

}