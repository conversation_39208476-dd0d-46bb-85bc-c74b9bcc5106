package com.chinamobile.sparrow.ai.service.dify.infra;

import com.chinamobile.sparrow.ai.service.dify.LoginFacade;
import com.chinamobile.sparrow.ai.service.dify.lang.Token;
import com.chinamobile.sparrow.domain.infra.code.Result;

public class TokenStore {

    Token token = null;

    final LoginFacade loginFacade;

    public TokenStore(LoginFacade loginFacade) {
        this.loginFacade = loginFacade;
    }

    public Token getToken() {
        return token;
    }

    public String refreshToken() {
        Result<Token> _token = loginFacade.login();
        if (_token.isOK()) {
            token = _token.data;
        }

        return token.getAccessToken();
    }

}