package com.chinamobile.sparrow.ai.service.dify;

import com.alibaba.fastjson.JSONPath;
import com.chinamobile.sparrow.ai.service.dify.infra.V1ApiBase;
import com.chinamobile.sparrow.ai.service.dify.lang.Dataset;
import com.chinamobile.sparrow.ai.service.dify.lang.Document;
import com.chinamobile.sparrow.ai.service.dify.lang.RetrievalModel;
import com.chinamobile.sparrow.ai.service.dify.lang.Segment;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PaginatedRecords;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import okhttp3.*;
import org.apache.commons.io.FilenameUtils;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class DatasetFacade extends V1ApiBase {

    public DatasetFacade(String baseUrl, String apiKey, ConnectionPool connectionPool) {
        super(baseUrl, apiKey, connectionPool);
    }

    public Result<Dataset> get(String id) {
        Request.Builder _builder = new Request.Builder()
                .url(baseUrl + "/v1/datasets/" + id)
                .get();

        return call(body -> {
            try {
                return body == null ? null : ConverterUtil.json2Object(body.string(), Dataset.class);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }, _builder);
    }

    public Result<String> create(String name, ENUM_INDEXING_TECHNIQUE indexingTechnique, String description, ENUM_PERMISSION permission) {
        JsonObject _json = new JsonObject();
        _json.addProperty("name", name);
        _json.addProperty("indexing_technique", indexingTechnique.name());

        if (StringUtils.hasLength(description)) {
            _json.addProperty("description", description);
        }

        if (permission != null) {
            _json.addProperty("permission", permission.name());
        }

        RequestBody _body = RequestBody.create(_json.toString(), MediaType.get(org.springframework.http.MediaType.APPLICATION_JSON_VALUE));

        Request.Builder _builder = new Request.Builder()
                .url(baseUrl + "/v1/datasets")
                .post(_body);

        return call(body -> {
            try {
                return body == null ? null : JSONPath.read(body.string(), "$.id", String.class);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }, _builder);
    }

    public Result<PaginatedRecords<Dataset>> find(Integer count, Integer index) {
        HttpUrl.Builder _url = HttpUrl.parse(baseUrl + "/v1/datasets").newBuilder();

        if (index != null) {
            _url.addQueryParameter("page", String.valueOf(index - 1));
        }

        if (count != null) {
            _url.addQueryParameter("limit", String.valueOf(count));
        }

        Request.Builder _builder = new Request.Builder()
                .url(_url.build())
                .get();

        return call(body -> {
            try {
                if (body == null) {
                    return new PaginatedRecords<>();
                }

                JsonObject _json = ConverterUtil.json2Object(body.string(), JsonObject.class);

                PaginatedRecords<Dataset> _page = new PaginatedRecords<>();
                _page.count = _json.get("limit").getAsInt();
                _page.index = _json.get("page").getAsInt();
                _page.total = _json.get("total").getAsInt();
                _page.records = new ArrayList<>();

                JsonArray _array = _json.get("data").getAsJsonArray();
                for (JsonElement i : _array) {
                    Dataset _record = ConverterUtil.json2Object(i.toString(), Dataset.class);
                    _page.records.add(_record);
                }

                return _page;
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }, _builder);
    }

    public Result<Void> delete(String id) {
        Request.Builder _builder = new Request.Builder()
                .url(baseUrl + "/v1/datasets/" + id)
                .delete();

        return call(body -> null, _builder);
    }

    public Result<List<Segment>> retrieve(String id, String query, RetrievalModel retrievalModel) {
        JsonObject _json = new JsonObject();
        _json.addProperty("query", query);

        if (retrievalModel != null) {
            _json.add("retrieval_model", ConverterUtil.gson.toJsonTree(retrievalModel));
        }

        RequestBody _body = RequestBody.create(_json.toString(), MediaType.get(org.springframework.http.MediaType.APPLICATION_JSON_VALUE));

        Request.Builder _builder = new Request.Builder()
                .url(baseUrl + "/v1/datasets/" + id + "/retrieve")
                .post(_body);

        return call(body -> {
            try {
                List<Segment> _segments = new ArrayList<>();

                if (body == null) {
                    return _segments;
                }

                JsonObject _temp = ConverterUtil.json2Object(body.string(), JsonObject.class);
                JsonArray _array = _temp.getAsJsonArray("records");
                for (JsonElement i : _array) {
                    _segments.add(ConverterUtil.json2Object(i.toString(), Segment.class));
                }
                return _segments;
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }, _builder);
    }

    public Result<String> uploadFile(String id, File file, ENUM_INDEXING_TECHNIQUE indexingTechnique) {
        JsonObject _json = new JsonObject();
        _json.addProperty("indexing_technique", indexingTechnique.name());

        JsonObject _processRule = new JsonObject();
        _processRule.addProperty("mode", ENUM_PROCESS_RULE_MODE.automatic.name());
        _json.add("process_rule", _processRule);

        String _fileName = FilenameUtils.getBaseName(file.getName()) + "_" + System.currentTimeMillis() + FilenameUtils.getExtension(file.getName());

        RequestBody _body = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("data", _json.toString())
                .addPart(
                        MultipartBody.Part.createFormData("file", _fileName, RequestBody.create(file, null))
                )
                .build();

        Request.Builder _builder = new Request.Builder()
                .url(baseUrl + "/v1/datasets/" + id + "/document/create-by-file")
                .post(_body);

        return call(body -> {
            try {
                return body == null ? null : JSONPath.read(body.string(), "$.document.id", String.class);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }, _builder);
    }

    public Result<String> uploadText(String id, String name, String text, ENUM_INDEXING_TECHNIQUE indexingTechnique) {
        JsonObject _json = new JsonObject();
        _json.addProperty("name", name);
        _json.addProperty("text", text);
        _json.addProperty("indexing_technique", indexingTechnique.name());

        JsonObject _processRule = new JsonObject();
        _processRule.addProperty("mode", ENUM_PROCESS_RULE_MODE.automatic.name());
        _json.add("process_rule", _processRule);

        RequestBody _body = RequestBody.create(_json.toString(), MediaType.get(org.springframework.http.MediaType.APPLICATION_JSON_VALUE));

        Request.Builder _builder = new Request.Builder()
                .url(baseUrl + "/v1/datasets/" + id + "/document/create-by-text")
                .post(_body);

        return call(body -> {
            try {
                return body == null ? null : JSONPath.read(body.string(), "$.document.id", String.class);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }, _builder);
    }

    public Result<com.chinamobile.sparrow.ai.service.dify.lang.File> getFile(String id, String documentId) {
        Request.Builder _builder = new Request.Builder()
                .url(baseUrl + "/v1/datasets/" + id + "/documents/" + documentId + "/upload-file")
                .get();

        return call(body -> {
            try {
                return body == null ? null : ConverterUtil.json2Object(body.string(), com.chinamobile.sparrow.ai.service.dify.lang.File.class);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }, _builder);
    }

    public Result<PaginatedRecords<Document>> searchDocuments(String id, Integer count, Integer index, String query) {
        HttpUrl.Builder _url = HttpUrl.parse(baseUrl + "/v1/datasets/" + id + "/documents").newBuilder();

        if (index != null) {
            _url.addQueryParameter("page", String.valueOf(index - 1));
        }

        if (count != null) {
            _url.addQueryParameter("limit", String.valueOf(count));
        }

        if (StringUtils.hasLength(query)) {
            _url.addQueryParameter("query", query);
        }

        Request.Builder _builder = new Request.Builder()
                .url(_url.build())
                .get();

        return call(body -> {
            try {
                if (body == null) {
                    return new PaginatedRecords<>();
                }

                JsonObject _json = ConverterUtil.json2Object(body.string(), JsonObject.class);

                PaginatedRecords<Document> _page = new PaginatedRecords<>();
                _page.count = _json.get("limit").getAsInt();
                _page.index = _json.get("page").getAsInt();
                _page.total = _json.get("total").getAsInt();
                _page.records = new ArrayList<>();

                JsonArray _array = _json.get("data").getAsJsonArray();
                for (JsonElement i : _array) {
                    _page.records.add(ConverterUtil.json2Object(i.toString(), Document.class));
                }

                return _page;
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }, _builder);
    }

    public Result<Void> deleteDocument(String id, String documentId) {
        Request.Builder _builder = new Request.Builder()
                .url(baseUrl + "/v1/datasets/" + id + "/documents/" + documentId)
                .delete();

        return call(body -> null, _builder);
    }

    public enum ENUM_PERMISSION {
        only_me, all_team_members, partial_members
    }

    public enum ENUM_PROVIDER {
        vendor, external
    }

    public enum ENUM_INDEXING_TECHNIQUE {
        high_quality, economy
    }

    public enum ENUM_DOC_FORM {
        txt_model, hierarchical_model, qa_model
    }

    public enum ENUM_PROCESS_RULE_MODE {
        automatic, custom
    }

}