package com.chinamobile.sparrow.ai.service.dify.lang;

import com.google.gson.annotations.SerializedName;

public class Memory {

    @SerializedName(value = "conversation_id")
    String conversationId;

    String query;
    String answer;

    @SerializedName(value = "created_at")
    Long createdAt;

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public String getQuery() {
        return query;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    public Long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Long createdAt) {
        this.createdAt = createdAt;
    }

}