package com.chinamobile.sparrow.ai.service.dify.lang;

public class RetrievalModel {

    RerankingModel reranking_model;
    String reranking_mode;
    String retrieval_model;
    Weights weights;
    boolean reranking_enable = false;
    boolean score_threshold_enabled = false;
    double score_threshold;
    int top_k;
    String search_method;

    public String getReranking_mode() {
        return reranking_mode;
    }

    public void setReranking_mode(String reranking_mode) {
        this.reranking_mode = reranking_mode;
    }

    public String getRetrieval_model() {
        return retrieval_model;
    }

    public void setRetrieval_model(String retrieval_model) {
        this.retrieval_model = retrieval_model;
    }

    public boolean isReranking_enable() {
        return reranking_enable;
    }

    public void setReranking_enable(boolean reranking_enable) {
        this.reranking_enable = reranking_enable;
    }

    public boolean isScore_threshold_enabled() {
        return score_threshold_enabled;
    }

    public void setScore_threshold_enabled(boolean score_threshold_enabled) {
        this.score_threshold_enabled = score_threshold_enabled;
    }

    public double getScore_threshold() {
        return score_threshold;
    }

    public void setScore_threshold(double score_threshold) {
        this.score_threshold = score_threshold;
    }

    public int getTop_k() {
        return top_k;
    }

    public void setTop_k(int top_k) {
        this.top_k = top_k;
    }

    public String getSearch_method() {
        return search_method;
    }

    public void setSearch_method(String search_method) {
        this.search_method = search_method;
    }

    public RerankingModel getReranking_model() {
        return reranking_model;
    }

    public void setReranking_model(RerankingModel reranking_model) {
        this.reranking_model = reranking_model;
    }

    public Weights getWeights() {
        return weights;
    }

    public void setWeights(Weights weights) {
        this.weights = weights;
    }

    public static class RerankingModel {

        String reranking_model_name;
        String reranking_provider_name;

        public String getReranking_model_name() {
            return reranking_model_name;
        }

        public void setReranking_model_name(String reranking_model_name) {
            this.reranking_model_name = reranking_model_name;
        }

        public String getReranking_provider_name() {
            return reranking_provider_name;
        }

        public void setReranking_provider_name(String reranking_provider_name) {
            this.reranking_provider_name = reranking_provider_name;
        }

    }

    public static class Weights {

        KeywordSetting keyword_setting;
        VectorSetting vector_setting;

        public KeywordSetting getKeyword_setting() {
            return keyword_setting;
        }

        public void setKeyword_setting(KeywordSetting keyword_setting) {
            this.keyword_setting = keyword_setting;
        }

        public VectorSetting getVector_setting() {
            return vector_setting;
        }

        public void setVector_setting(VectorSetting vector_setting) {
            this.vector_setting = vector_setting;
        }

        public static class KeywordSetting {

            double keyword_weight;

            public double getKeyword_weight() {
                return keyword_weight;
            }

            public void setKeyword_weight(double keyword_weight) {
                this.keyword_weight = keyword_weight;
            }
        }

        public static class VectorSetting {

            String embedding_model_name;
            String embedding_provider_name;
            double vector_weight;

            public String getEmbedding_model_name() {
                return embedding_model_name;
            }

            public void setEmbedding_model_name(String embedding_model_name) {
                this.embedding_model_name = embedding_model_name;
            }

            public String getEmbedding_provider_name() {
                return embedding_provider_name;
            }

            public void setEmbedding_provider_name(String embedding_provider_name) {
                this.embedding_provider_name = embedding_provider_name;
            }

            public double getVector_weight() {
                return vector_weight;
            }

            public void setVector_weight(double vector_weight) {
                this.vector_weight = vector_weight;
            }

        }

    }

}