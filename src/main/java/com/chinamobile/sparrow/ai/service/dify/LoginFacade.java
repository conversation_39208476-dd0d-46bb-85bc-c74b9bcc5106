package com.chinamobile.sparrow.ai.service.dify;

import com.alibaba.fastjson.JSONPath;
import com.chinamobile.sparrow.ai.service.dify.infra.ApiBase;
import com.chinamobile.sparrow.ai.service.dify.lang.Token;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.util.HttpUtil;
import com.google.gson.JsonObject;
import okhttp3.*;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

import static com.chinamobile.sparrow.domain.util.HttpUtil.createUnsafeSSLContext;

public class LoginFacade extends ApiBase {

    protected final String account;
    protected final String password;

    public LoginFacade(
            String baseUrl,
            String account,
            String password,
            ConnectionPool connectionPool
    ) {
        super(baseUrl);
        this.client = new OkHttpClient.Builder()
                .connectionPool(connectionPool == null ? new ConnectionPool() : connectionPool)
                .readTimeout(10, TimeUnit.MINUTES)
                .sslSocketFactory(createUnsafeSSLContext().getSocketFactory(), new HttpUtil.TrustAllCertsManager())
                .hostnameVerifier(new HttpUtil.TrustAllHostnameVerifier())
                .build();

        this.account = account;
        this.password = password;
    }

    public Result<Token> login() {
        JsonObject _json = new JsonObject();
        _json.addProperty("email", account);
        _json.addProperty("password", password);
        _json.addProperty("language", "zh-Hans");

        RequestBody _body = RequestBody.create(_json.toString(), MediaType.get(org.springframework.http.MediaType.APPLICATION_JSON_VALUE));

        Request.Builder _builder = new Request.Builder()
                .url(baseUrl + "/console/api/login")
                .post(_body);

        return call(body -> {
            try {
                String _temp = body.string();

                Token _token = new Token();
                _token.setAccessToken(JSONPath.read(_temp, "$.data.access_token", String.class));
                _token.setRefreshToken(JSONPath.read(_temp, "$.data.refresh_token", String.class));
                return _token;
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }, _builder);
    }

}