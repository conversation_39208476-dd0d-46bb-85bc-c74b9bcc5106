package com.chinamobile.sparrow.ai.service.dify.lang;

import com.google.gson.annotations.SerializedName;

public class WorkflowResponse<T> {

    public static final String CODE_OK = "succeeded";

    @SerializedName("task_id")
    String taskId;

    @SerializedName("workflow_run_id")
    String workflowRunId;

    Data<T> data;

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getWorkflowRunId() {
        return workflowRunId;
    }

    public void setWorkflowRunId(String workflowRunId) {
        this.workflowRunId = workflowRunId;
    }

    public Data<T> getData() {
        return data;
    }

    public void setData(Data<T> data) {
        this.data = data;
    }

    public static class Data<T> {

        String id;

        @SerializedName("workflow_id")
        String workflowId;

        String status;

        T outputs;

        String error;

        @SerializedName("elapsed_time")
        double elapsedTime;

        @SerializedName("total_tokens")
        int totalTokens;

        @SerializedName("total_steps")
        int totalSteps;

        @SerializedName("created_at")
        Long createdAt;

        @SerializedName("finished_at")
        Long finishedAt;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getWorkflowId() {
            return workflowId;
        }

        public void setWorkflowId(String workflowId) {
            this.workflowId = workflowId;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public T getOutputs() {
            return outputs;
        }

        public void setOutputs(T outputs) {
            this.outputs = outputs;
        }

        public String getError() {
            return error;
        }

        public void setError(String error) {
            this.error = error;
        }

        public double getElapsedTime() {
            return elapsedTime;
        }

        public void setElapsedTime(double elapsedTime) {
            this.elapsedTime = elapsedTime;
        }

        public int getTotalTokens() {
            return totalTokens;
        }

        public void setTotalTokens(int totalTokens) {
            this.totalTokens = totalTokens;
        }

        public int getTotalSteps() {
            return totalSteps;
        }

        public void setTotalSteps(int totalSteps) {
            this.totalSteps = totalSteps;
        }

        public Long getCreatedAt() {
            return createdAt;
        }

        public void setCreatedAt(Long createdAt) {
            this.createdAt = createdAt;
        }

        public Long getFinishedAt() {
            return finishedAt;
        }

        public void setFinishedAt(Long finishedAt) {
            this.finishedAt = finishedAt;
        }

    }

}