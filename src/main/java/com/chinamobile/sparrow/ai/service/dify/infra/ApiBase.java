package com.chinamobile.sparrow.ai.service.dify.infra;

import com.chinamobile.sparrow.domain.infra.code.Result;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;

import java.util.function.Function;

public abstract class ApiBase {

    protected final String baseUrl;
    protected OkHttpClient client;

    protected ApiBase(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    protected <T> Result<T> call(Function<ResponseBody, T> function, Request.Builder builder) {
        Result<T> _result = new Result<>();

        try (Response response = client.newCall(builder.build()).execute()) {
            if (response.isSuccessful()) {
                _result.data = function.apply(response.body());
            } else {
                _result.setCode(Result.ENUM_ERROR.N, response.code());
                _result.message = response.message();
            }
        } catch (Throwable e) {
            _result.setCode(Result.SERVICE_UNKNOWN);
            _result.message = e.getMessage();
        }

        return _result;
    }

    protected <T> Result<T> callResult(Function<ResponseBody, Result<T>> function, Request.Builder builder) {
        try (Response response = client.newCall(builder.build()).execute()) {
            return function.apply(response.body());
        } catch (Throwable e) {
            Result<T> _result = new Result<>();
            _result.setCode(Result.SERVICE_UNKNOWN);
            _result.message = e.getMessage();
            return _result;
        }
    }

}