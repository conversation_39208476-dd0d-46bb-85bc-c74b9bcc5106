package com.chinamobile.sparrow.ai.service.dify.lang;

import com.google.gson.annotations.SerializedName;

public class ChatResponse {

    String event;

    @SerializedName("task_id")
    String taskId;

    String id;

    @SerializedName("message_id")
    String messageId;

    @SerializedName("conversation_id")
    String conversationId;

    String mode;

    String answer;

    Metadata metadata;

    @SerializedName("created_at")
    Long createdAt;

    public String getEvent() {
        return event;
    }

    public void setEvent(String event) {
        this.event = event;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    public Metadata getMetadata() {
        return metadata;
    }

    public void setMetadata(Metadata metadata) {
        this.metadata = metadata;
    }

    public Long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Long createdAt) {
        this.createdAt = createdAt;
    }

    public static class Metadata {

        Usage usage;

        public Usage getUsage() {
            return usage;
        }

        public void setUsage(Usage usage) {
            this.usage = usage;
        }
    }

    public static class Usage {

        @SerializedName("prompt_tokens")
        Integer promptTokens;

        @SerializedName("prompt_unit_price")
        String promptUnitPrice;

        @SerializedName("prompt_price_unit")
        String promptPriceUnit;

        @SerializedName("prompt_price")
        String promptPrice;

        @SerializedName("completion_tokens")
        Integer completionTokens;

        @SerializedName("completion_unit_price")
        String completionUnitPrice;

        @SerializedName("completion_price_unit")
        String completionPriceUnit;

        @SerializedName("completion_price")
        String completionPrice;

        @SerializedName("total_tokens")
        Integer totalTokens;

        @SerializedName("total_price")
        String totalPrice;

        String currency;

        Double latency;

        public Integer getPromptTokens() {
            return promptTokens;
        }

        public void setPromptTokens(Integer promptTokens) {
            this.promptTokens = promptTokens;
        }

        public String getPromptUnitPrice() {
            return promptUnitPrice;
        }

        public void setPromptUnitPrice(String promptUnitPrice) {
            this.promptUnitPrice = promptUnitPrice;
        }

        public String getPromptPriceUnit() {
            return promptPriceUnit;
        }

        public void setPromptPriceUnit(String promptPriceUnit) {
            this.promptPriceUnit = promptPriceUnit;
        }

        public String getPromptPrice() {
            return promptPrice;
        }

        public void setPromptPrice(String promptPrice) {
            this.promptPrice = promptPrice;
        }

        public Integer getCompletionTokens() {
            return completionTokens;
        }

        public void setCompletionTokens(Integer completionTokens) {
            this.completionTokens = completionTokens;
        }

        public String getCompletionUnitPrice() {
            return completionUnitPrice;
        }

        public void setCompletionUnitPrice(String completionUnitPrice) {
            this.completionUnitPrice = completionUnitPrice;
        }

        public String getCompletionPriceUnit() {
            return completionPriceUnit;
        }

        public void setCompletionPriceUnit(String completionPriceUnit) {
            this.completionPriceUnit = completionPriceUnit;
        }

        public String getCompletionPrice() {
            return completionPrice;
        }

        public void setCompletionPrice(String completionPrice) {
            this.completionPrice = completionPrice;
        }

        public Integer getTotalTokens() {
            return totalTokens;
        }

        public void setTotalTokens(Integer totalTokens) {
            this.totalTokens = totalTokens;
        }

        public String getTotalPrice() {
            return totalPrice;
        }

        public void setTotalPrice(String totalPrice) {
            this.totalPrice = totalPrice;
        }

        public String getCurrency() {
            return currency;
        }

        public void setCurrency(String currency) {
            this.currency = currency;
        }

        public Double getLatency() {
            return latency;
        }

        public void setLatency(Double latency) {
            this.latency = latency;
        }

    }

}