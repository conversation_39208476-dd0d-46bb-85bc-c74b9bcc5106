package com.chinamobile.sparrow.ai.service.dify;

import com.alibaba.fastjson.JSONPath;
import com.chinamobile.sparrow.ai.service.dify.infra.ApiBase;
import com.chinamobile.sparrow.ai.service.dify.lang.ChatResponse;
import com.chinamobile.sparrow.ai.service.dify.lang.WorkflowResponse;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.HttpUtil;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonObject;
import okhttp3.*;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import okhttp3.sse.EventSources;
import org.springframework.http.HttpHeaders;

import java.io.File;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

import static com.chinamobile.sparrow.domain.util.HttpUtil.createUnsafeSSLContext;

public class AppFacade extends ApiBase {

    public AppFacade(String baseUrl, ConnectionPool connectionPool) {
        super(baseUrl);
        this.client = new OkHttpClient.Builder()
                .connectionPool(connectionPool == null ? new ConnectionPool() : connectionPool)
                .readTimeout(10, TimeUnit.MINUTES)
                .sslSocketFactory(createUnsafeSSLContext().getSocketFactory(), new HttpUtil.TrustAllCertsManager())
                .hostnameVerifier(new HttpUtil.TrustAllHostnameVerifier())
                .build();
    }

    public Result<String> uploadFile(String apiKey, File file, String user) {
        RequestBody _body = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("user", user)
                .addPart(
                        MultipartBody.Part.createFormData("file", file.getName(), RequestBody.create(file, MediaType.get(org.springframework.http.MediaType.TEXT_PLAIN_VALUE)))
                )
                .build();

        Request.Builder _builder = new Request.Builder()
                .url(baseUrl + "/v1/files/upload")
                .addHeader(HttpHeaders.AUTHORIZATION, "Bearer " + apiKey)
                .post(_body);

        return call((body) -> {
            try {
                return body == null ? null : JSONPath.read(body.string(), "$.id", String.class);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }, _builder);
    }

    public EventSource chat(String apiKey, String conversationId, JsonObject inputs, String query, String user, EventSourceListener listener) {
        Request.Builder _builder = buildChatRequest("streaming", apiKey, conversationId, inputs, query, user);

        return EventSources.createFactory(client).newEventSource(_builder.build(), listener);
    }

    public Result<String> chat(String apiKey, String conversationId, JsonObject inputs, String query, String user) {
        Request.Builder _builder = buildChatRequest("blocking", apiKey, conversationId, inputs, query, user);

        return call((body) -> {
            try {
                ChatResponse _temp = ConverterUtil.json2Object(body.string(), ChatResponse.class);
                return _temp.getAnswer();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }, _builder);
    }

    protected Request.Builder buildChatRequest(String responseMode, String apiKey, String conversationId, JsonObject inputs, String query, String user) {
        JsonObject _json = new JsonObject();
        _json.addProperty("response_mode", responseMode);
        _json.addProperty("conversation_id", conversationId == null ? "" : conversationId);
        _json.add("inputs", inputs == null ? new JsonObject() : null);
        _json.addProperty("query", query);
        _json.addProperty("user", user);

        RequestBody _body = RequestBody.create(_json.toString(), MediaType.get(org.springframework.http.MediaType.APPLICATION_JSON_VALUE));
        return new Request.Builder()
                .url(baseUrl + "/v1/chat-message")
                .addHeader(HttpHeaders.AUTHORIZATION, String.format("Bearer %s", apiKey))
                .post(_body);
    }

    public EventSource workflow(String apiKey, JsonObject inputs, String user, EventSourceListener listener) {
        Request.Builder _builder = buildWorkflowRequest("streaming", apiKey, inputs, user);

        return EventSources.createFactory(client).newEventSource(_builder.build(), listener);
    }

    public <T> Result<T> workflow(String apiKey, JsonObject inputs, String user) {
        JsonObject _json = new JsonObject();
        _json.addProperty("response_mode", "blocking");

        Request.Builder _builder = buildWorkflowRequest("blocking", apiKey, inputs, user);

        return callResult((body) -> {
            Result<T> _output = new Result<>();

            try {
                WorkflowResponse<T> _temp = ConverterUtil.json2Object(body.string(), new TypeToken<WorkflowResponse<T>>() {
                }.getType());

                if (WorkflowResponse.CODE_OK.equals(_temp.getData().getStatus())) {
                    _output.data = _temp.getData().getOutputs();
                } else {
                    _output.setCode(_temp.getData().getStatus());
                    _output.message = _temp.getData().getError();
                }
            } catch (IOException e) {
                _output.setCode(Result.SERVICE_UNKNOWN);
                _output.message = e.getMessage();
            }

            return _output;
        }, _builder);
    }

    protected Request.Builder buildWorkflowRequest(String responseMode, String apiKey, JsonObject inputs, String user) {
        JsonObject _json = new JsonObject();
        _json.addProperty("response_mode", responseMode);
        _json.add("inputs", inputs);
        _json.addProperty("user", user);
        RequestBody _body = RequestBody.create(_json.toString(), MediaType.get(org.springframework.http.MediaType.APPLICATION_JSON_VALUE));

        return new Request.Builder()
                .url(baseUrl + "/v1/workflows/run")
                .addHeader(HttpHeaders.AUTHORIZATION, String.format("Bearer %s", apiKey))
                .post(_body);
    }

}