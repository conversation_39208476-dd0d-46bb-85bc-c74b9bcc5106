package com.chinamobile.sparrow.ai.service.dify.infra;

import com.chinamobile.sparrow.ai.service.dify.lang.Token;
import com.chinamobile.sparrow.domain.util.HttpUtil;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import org.springframework.http.HttpHeaders;

import java.util.concurrent.TimeUnit;

import static com.chinamobile.sparrow.domain.util.HttpUtil.createUnsafeSSLContext;

public class ConsoleApiBase extends ApiBase {

    protected final TokenStore tokenStore;

    public ConsoleApiBase(String baseUrl, ConnectionPool connectionPool, TokenStore tokenStore) {
        super(baseUrl);

        this.tokenStore = tokenStore;
        this.client = new OkHttpClient.Builder()
                .connectionPool(connectionPool == null ? new ConnectionPool() : connectionPool)
                .readTimeout(10, TimeUnit.MINUTES)
                .sslSocketFactory(createUnsafeSSLContext().getSocketFactory(), new HttpUtil.TrustAllCertsManager())
                .hostnameVerifier(new HttpUtil.TrustAllHostnameVerifier())
                .addInterceptor(chain -> {
                    Token _token = tokenStore.getToken();
                    if (_token == null) {
                        return chain.proceed(chain.request());
                    }

                    Request.Builder builder = chain.request().newBuilder()
                            .addHeader(HttpHeaders.CONTENT_TYPE, org.springframework.http.MediaType.APPLICATION_JSON_VALUE)
                            .addHeader(HttpHeaders.AUTHORIZATION, String.format("Bearer %s", _token.getAccessToken()));
                    return chain.proceed(builder.build());
                })
                .authenticator(new LoginAuthenticator(tokenStore))
                .build();
    }

    public Token getToken() {
        if (tokenStore.getToken() == null) {
            tokenStore.refreshToken();
        }

        return tokenStore.getToken();
    }

}