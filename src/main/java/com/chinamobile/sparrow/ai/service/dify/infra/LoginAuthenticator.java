package com.chinamobile.sparrow.ai.service.dify.infra;

import okhttp3.Authenticator;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.Route;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class LoginAuthenticator implements Authenticator {

    final TokenStore tokenStore;

    public LoginAuthenticator(TokenStore tokenStore) {
        this.tokenStore = tokenStore;
    }

    @Nullable
    @Override
    public Request authenticate(@Nullable Route route, @NotNull Response response) {
        if (response.priorResponse() != null) {
            return null;
        }

        // 登录失败，刷新access_token
        tokenStore.refreshToken();

        return response.request();
    }

}