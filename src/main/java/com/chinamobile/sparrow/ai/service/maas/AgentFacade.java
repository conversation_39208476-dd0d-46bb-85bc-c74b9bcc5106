package com.chinamobile.sparrow.ai.service.maas;

import com.chinamobile.sparrow.domain.util.HttpUtil;
import com.google.gson.JsonObject;
import okhttp3.*;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import okhttp3.sse.EventSources;
import org.springframework.http.HttpHeaders;
import org.springframework.util.Base64Utils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.concurrent.TimeUnit;

import static com.chinamobile.sparrow.domain.util.HttpUtil.createUnsafeSSLContext;

public class AgentFacade {

    final String ALGORITHM = "SHA1";

    final String url;
    final String accessKeyId;
    final String accessKeySecret;
    final OkHttpClient client;

    public AgentFacade(String url, String accessKeyId, String accessKeySecret, ConnectionPool connectionPool) {
        this.url = url;
        this.accessKeyId = accessKeyId;
        this.accessKeySecret = accessKeySecret;

        client = new OkHttpClient.Builder()
                .connectionPool(connectionPool == null ? new ConnectionPool() : connectionPool)
                .readTimeout(10, TimeUnit.MINUTES)
                .sslSocketFactory(createUnsafeSSLContext().getSocketFactory(), new HttpUtil.TrustAllCertsManager())
                .hostnameVerifier(new HttpUtil.TrustAllHostnameVerifier())
                .build();
    }

    public EventSource chat(String model, String query, EventSourceListener listener) throws Exception {
        JsonObject _json = new JsonObject();
        _json.addProperty("ResponseMode", "streaming");
        _json.addProperty("Query", query);

        Request.Builder _builder = new Request.Builder()
                .url(url + model)
                .header(HttpHeaders.ACCEPT, org.springframework.http.MediaType.TEXT_EVENT_STREAM_VALUE);

        // 签名
        sign(_builder, _json);

        Request _request = _builder.post(RequestBody.create(_json.toString(), MediaType.get(org.springframework.http.MediaType.APPLICATION_JSON_VALUE))).build();

        return EventSources.createFactory(client).newEventSource(_request, listener);
    }

    void sign(Request.Builder builder, JsonObject data) throws NoSuchAlgorithmException, InvalidKeyException {
        String _timestamp = String.valueOf(System.currentTimeMillis());
        String _json = data.toString();
        String _policy = accessKeyId + accessKeySecret + _timestamp + _json.substring(0, Math.min(_json.length(), 512));
        String _signature = sign(accessKeySecret, _policy);

        builder.header("Accesskey", accessKeyId)
                .header("Signtype", ALGORITHM)
                .header("Sign", _signature)
                .header("Timestamp", _timestamp);
    }

    String sign(String key, String data) throws NoSuchAlgorithmException, InvalidKeyException {
        SecretKeySpec _secretKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "HmacSHA1");

        Mac _hmac = Mac.getInstance("HmacSHA1");
        _hmac.init(_secretKey);
        byte[] _hash = _hmac.doFinal(data.getBytes(StandardCharsets.UTF_8));

        return Base64Utils.encodeToString(_hash);
    }

}