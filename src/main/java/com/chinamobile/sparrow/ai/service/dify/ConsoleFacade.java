package com.chinamobile.sparrow.ai.service.dify;

import com.alibaba.fastjson.JSONPath;
import com.chinamobile.sparrow.ai.model.llm.Memory;
import com.chinamobile.sparrow.ai.service.dify.infra.ConsoleApiBase;
import com.chinamobile.sparrow.ai.service.dify.infra.TokenStore;
import com.chinamobile.sparrow.ai.service.dify.lang.RetrievalModel;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PaginatedRecords;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import okhttp3.*;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import okhttp3.sse.EventSources;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.*;
import java.util.function.Function;

public class ConsoleFacade extends ConsoleApiBase {

    public ConsoleFacade(String baseUrl, ConnectionPool connectionPool, TokenStore tokenStore) {
        super(baseUrl, connectionPool, tokenStore);
    }

    public EventSource chat(String appId, String conversationId, String model, String provider, String prompt, List<String> datasetIds, String retrievalModel, Integer topK, Double score, String rerankingModelName, String rerankingProviderName, String message, EventSourceListener listener) {
        return chat(config -> {
            config.setReranking_enable(true);
            config.setReranking_mode("reranking_model");

            RetrievalModel.RerankingModel _rerankingModel = new RetrievalModel.RerankingModel();
            _rerankingModel.setReranking_model_name(rerankingModelName);
            _rerankingModel.setReranking_provider_name(rerankingProviderName);
            config.setReranking_model(_rerankingModel);

            return config;
        }, appId, conversationId, model, provider, prompt, datasetIds, retrievalModel, topK, score, message, listener);
    }

    public EventSource chat(String appId, String conversationId, String model, String provider, String prompt, List<String> datasetIds, String retrievalModel, Integer topK, Double score, double vectorWeight, double keywordWeight, String message, EventSourceListener listener) {
        return chat(config -> {
            config.setReranking_enable(true);
            config.setReranking_mode("weighted_score");

            DatasetConfigs.Weights.KeywordSetting _keywordSetting = new DatasetConfigs.Weights.KeywordSetting();
            _keywordSetting.setKeyword_weight(keywordWeight);
            DatasetConfigs.Weights.VectorSetting _vectorSetting = new DatasetConfigs.Weights.VectorSetting();
            _vectorSetting.setVector_weight(vectorWeight);
            DatasetConfigs.Weights _weights = new DatasetConfigs.Weights();
            _weights.setKeyword_setting(_keywordSetting);
            _weights.setVector_setting(_vectorSetting);
            config.setWeights(_weights);

            return config;
        }, appId, conversationId, model, provider, prompt, datasetIds, retrievalModel, topK, score, message, listener);
    }

    protected EventSource chat(Function<DatasetConfigs, DatasetConfigs> func, String appId, String conversationId, String model, String provider, String prompt, List<String> datasetIds, String retrievalModel, Integer topK, Double score, String message, EventSourceListener listener) {
        JsonObject _json = new JsonObject();

        _json.addProperty("response_mode", "streaming");
        _json.addProperty("conversation_id", conversationId);
        _json.addProperty("query", message);
        _json.add("inputs", new JsonObject());

        ModelConfig _modelConfig = new ModelConfig();
        _modelConfig.setModel(new Model(model, provider));

        // 设置提示词
        if (StringUtils.hasLength(prompt)) {
            _modelConfig.setPre_prompt(prompt);
        }

        // 设置知识库
        if (!CollectionUtils.isEmpty(datasetIds)) {
            List<DatasetConfigs.Datasets.DatasetWrapper> _datasets = new ArrayList<>();
            datasetIds.forEach(i -> {
                DatasetConfigs.Datasets.DatasetWrapper.Dataset _dataset = new DatasetConfigs.Datasets.DatasetWrapper.Dataset(i);
                DatasetConfigs.Datasets.DatasetWrapper _wrapper = new DatasetConfigs.Datasets.DatasetWrapper();
                _wrapper.setDataset(_dataset);

                _datasets.add(_wrapper);
            });

            DatasetConfigs.Datasets _temp = new DatasetConfigs.Datasets();
            _temp.datasets = _datasets;

            DatasetConfigs _config = new DatasetConfigs();
            _config.setDatasets(_temp);

            _config.setRetrieval_model(retrievalModel);
            _config.setTop_k(topK);

            if (score != null) {
                _config.setScore_threshold_enabled(true);
                _config.setScore_threshold(score);
            }

            _config.setReranking_enable(false);

            _config = func.apply(_config);

            _modelConfig.setDataset_configs(_config);
        }

        _json.add("model_config", ConverterUtil.gson.toJsonTree(_modelConfig));

        RequestBody _body = RequestBody.create(_json.toString(), MediaType.get(org.springframework.http.MediaType.APPLICATION_JSON_VALUE));

        Request _request = new Request.Builder()
                .url(baseUrl + "/console/api/apps/" + appId + "/chat-messages")
                .post(_body)
                .build();

        return EventSources.createFactory(client).newEventSource(_request, listener);
    }

    public Result<List<Memory>> memories(String appId, String conversationId, String userId) {
        HttpUrl.Builder _url = HttpUrl.parse(baseUrl + "/console/api/apps/" + appId + "/chat-messages").newBuilder();
        _url.addQueryParameter("conversation_id", conversationId);

        Request.Builder _builder = new Request.Builder()
                .url(_url.build())
                .get();

        return call(body -> {
            try {
                if (body == null) {
                    return new ArrayList<>();
                }

                List<com.chinamobile.sparrow.ai.service.dify.lang.Memory> _temp = ConverterUtil.json2Object(JSONPath.read(body.string(), "$.data", String.class), new TypeToken<List<com.chinamobile.sparrow.ai.service.dify.lang.Memory>>() {
                }.getType());

                List<Memory> _memories = new ArrayList<>();
                for (com.chinamobile.sparrow.ai.service.dify.lang.Memory i : _temp) {
                    Memory _memory = new Memory();
                    _memory.setConversationId(i.getConversationId());
                    _memory.setMessage(i.getQuery());
                    _memory.setUserId(userId);
                    _memory.setTimestamp(new Date(i.getCreatedAt() * 1000L));
                    _memories.add(_memory);

                    _memory = new Memory();
                    _memory.setConversationId(i.getConversationId());
                    _memory.setMessage(i.getAnswer());
                    _memory.setTimestamp(new Date(i.getCreatedAt() * 1000L));
                    _memories.add(_memory);
                }

                return _memories;
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }, _builder);
    }

    public Result<PaginatedRecords<Application>> searchApps(Integer count, Integer index, String name) {
        HttpUrl.Builder _url = HttpUrl.parse(baseUrl + "/console/api/apps").newBuilder();
        _url.addQueryParameter("page", index == null ? "1" : String.valueOf(index + 1));
        _url.addQueryParameter("limit", count == null ? "100" : String.valueOf(count));

        if (name != null) {
            _url.addQueryParameter("name", name);
        }

        Request.Builder _builder = new Request.Builder()
                .url(_url.build())
                .get();

        return call(body -> {
            try {
                String _temp = body.string();

                PaginatedRecords<Application> _page = new PaginatedRecords<>();
                _page.count = JSONPath.read(_temp, "$.limit", Integer.class);
                _page.index = (int) JSONPath.read(_temp, "$.page", Integer.class) - 1;
                _page.total = JSONPath.read(_temp, "$.total", Long.class);
                _page.records = ConverterUtil.json2Object(JSONPath.read(_temp, "$.data", String.class), new TypeToken<List<Application>>() {
                }.getType());

                return _page;
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }, _builder);
    }

    public Result<List<Application>> installApps() {
        Request.Builder _builder = new Request.Builder()
                .url(baseUrl + "/console/api/installed-apps")
                .get();

        return call(body -> {
            try {
                JsonArray _json = ConverterUtil.json2Object(body.string(), JsonObject.class).get("installed_apps").getAsJsonArray();

                List<Application> _applications = new ArrayList<>();
                _json.forEach(i -> {
                    JsonObject _el = i.getAsJsonObject();
                    Application _application = ConverterUtil.json2Object(_el.get("app").toString(), Application.class);
                    _application.setInstallId(_el.get("id").getAsString());

                    _applications.add(_application);
                });

                return _applications;
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }, _builder);
    }

    public Result<Void> removeApp(String id) {
        Request.Builder _builder = new Request.Builder()
                .url(baseUrl + "/console/api/apps/" + id)
                .delete();

        return call(body -> null, _builder);
    }

    public static class Application {

        String id;
        String installId;
        String icon;
        String icon_url;
        String name;
        String description;
        List<Tag> tags;
        Long created_at;
        Long updated_at;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getInstallId() {
            return installId;
        }

        public void setInstallId(String installId) {
            this.installId = installId;
        }

        public String getIcon() {
            return icon;
        }

        public void setIcon(String icon) {
            this.icon = icon;
        }

        public String getIcon_url() {
            return icon_url;
        }

        public void setIcon_url(String icon_url) {
            this.icon_url = icon_url;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public List<Tag> getTags() {
            return tags;
        }

        public void setTags(List<Tag> tags) {
            this.tags = tags;
        }

        public Long getCreated_at() {
            return created_at;
        }

        public void setCreated_at(Long created_at) {
            this.created_at = created_at;
        }

        public Long getUpdated_at() {
            return updated_at;
        }

        public void setUpdated_at(Long updated_at) {
            this.updated_at = updated_at;
        }

    }

    protected static class Tag {

        String id;
        String name;
        String type;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

    }

    protected static class ModelConfig {

        AgentMode agent_mode;
        DatasetConfigs dataset_configs;
        FileUpload file_upload;
        List<Object> suggested_questions;
        List<Object> user_input_form;
        Map<String, Object> chat_prompt_config;
        Map<String, Object> completion_prompt_config;
        Model model;
        MoreLikeThis more_like_this;
        RetrieverResource retriever_resource;
        SensitiveWordAvoidance sensitive_word_avoidance;
        SpeechToText speech_to_text;
        String appId;
        String dataset_query_variable;
        String opening_statement;
        String pre_prompt;
        String prompt_type;
        SuggestedQuestionsAfterAnswer suggested_questions_after_answer;
        TextToSpeech text_to_speech;

        public AgentMode getAgent_mode() {
            return agent_mode;
        }

        public void setAgent_mode(AgentMode agent_mode) {
            this.agent_mode = agent_mode;
        }

        public DatasetConfigs getDataset_configs() {
            return dataset_configs;
        }

        public void setDataset_configs(DatasetConfigs dataset_configs) {
            this.dataset_configs = dataset_configs;
        }

        public FileUpload getFile_upload() {
            return file_upload;
        }

        public void setFile_upload(FileUpload file_upload) {
            this.file_upload = file_upload;
        }

        public List<Object> getSuggested_questions() {
            return suggested_questions;
        }

        public void setSuggested_questions(List<Object> suggested_questions) {
            this.suggested_questions = suggested_questions;
        }

        public List<Object> getUser_input_form() {
            return user_input_form;
        }

        public void setUser_input_form(List<Object> user_input_form) {
            this.user_input_form = user_input_form;
        }

        public Map<String, Object> getChat_prompt_config() {
            return chat_prompt_config;
        }

        public void setChat_prompt_config(Map<String, Object> chat_prompt_config) {
            this.chat_prompt_config = chat_prompt_config;
        }

        public Map<String, Object> getCompletion_prompt_config() {
            return completion_prompt_config;
        }

        public void setCompletion_prompt_config(Map<String, Object> completion_prompt_config) {
            this.completion_prompt_config = completion_prompt_config;
        }

        public Model getModel() {
            return model;
        }

        public void setModel(Model model) {
            this.model = model;
        }

        public MoreLikeThis getMore_like_this() {
            return more_like_this;
        }

        public void setMore_like_this(MoreLikeThis more_like_this) {
            this.more_like_this = more_like_this;
        }

        public RetrieverResource getRetriever_resource() {
            return retriever_resource;
        }

        public void setRetriever_resource(RetrieverResource retriever_resource) {
            this.retriever_resource = retriever_resource;
        }

        public SensitiveWordAvoidance getSensitive_word_avoidance() {
            return sensitive_word_avoidance;
        }

        public void setSensitive_word_avoidance(SensitiveWordAvoidance sensitive_word_avoidance) {
            this.sensitive_word_avoidance = sensitive_word_avoidance;
        }

        public SpeechToText getSpeech_to_text() {
            return speech_to_text;
        }

        public void setSpeech_to_text(SpeechToText speech_to_text) {
            this.speech_to_text = speech_to_text;
        }

        public String getAppId() {
            return appId;
        }

        public void setAppId(String appId) {
            this.appId = appId;
        }

        public String getDataset_query_variable() {
            return dataset_query_variable;
        }

        public void setDataset_query_variable(String dataset_query_variable) {
            this.dataset_query_variable = dataset_query_variable;
        }

        public String getOpening_statement() {
            return opening_statement;
        }

        public void setOpening_statement(String opening_statement) {
            this.opening_statement = opening_statement;
        }

        public String getPre_prompt() {
            return pre_prompt;
        }

        public void setPre_prompt(String pre_prompt) {
            this.pre_prompt = pre_prompt;
        }

        public String getPrompt_type() {
            return prompt_type;
        }

        public void setPrompt_type(String prompt_type) {
            this.prompt_type = prompt_type;
        }

        public SuggestedQuestionsAfterAnswer getSuggested_questions_after_answer() {
            return suggested_questions_after_answer;
        }

        public void setSuggested_questions_after_answer(SuggestedQuestionsAfterAnswer suggested_questions_after_answer) {
            this.suggested_questions_after_answer = suggested_questions_after_answer;
        }

        public TextToSpeech getText_to_speech() {
            return text_to_speech;
        }

        public void setText_to_speech(TextToSpeech text_to_speech) {
            this.text_to_speech = text_to_speech;
        }

    }

    protected static class AgentMode {

        List<Object> tools;
        String strategy;
        boolean enabled = false;
        int max_iteration;

        public List<Object> getTools() {
            return tools;
        }

        public void setTools(List<Object> tools) {
            this.tools = tools;
        }

        public String getStrategy() {
            return strategy;
        }

        public void setStrategy(String strategy) {
            this.strategy = strategy;
        }

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public int getMax_iteration() {
            return max_iteration;
        }

        public void setMax_iteration(int max_iteration) {
            this.max_iteration = max_iteration;
        }

    }

    protected static class DatasetConfigs extends RetrievalModel {

        Datasets datasets;

        public Datasets getDatasets() {
            return datasets;
        }

        public void setDatasets(Datasets datasets) {
            this.datasets = datasets;
        }

        protected static class Datasets {

            List<DatasetWrapper> datasets;

            public List<DatasetWrapper> getDatasets() {
                return datasets;
            }

            public void setDatasets(List<DatasetWrapper> datasets) {
                this.datasets = datasets;
            }

            protected static class DatasetWrapper {

                Dataset dataset;

                public Dataset getDataset() {
                    return dataset;
                }

                public void setDataset(Dataset dataset) {
                    this.dataset = dataset;
                }

                protected static class Dataset {

                    String id;
                    boolean enabled = true;

                    public Dataset(String id) {
                        this.id = id;
                    }

                    public String getId() {
                        return id;
                    }

                    public void setId(String id) {
                        this.id = id;
                    }

                    public boolean isEnabled() {
                        return enabled;
                    }

                    public void setEnabled(boolean enabled) {
                        this.enabled = enabled;
                    }

                }

            }

        }

    }

    protected static class FileUpload {

        Image image;
        List<String> allowed_file_extensions;
        List<String> allowed_file_types;
        List<String> allowed_file_upload_methods;
        boolean enabled;
        int number_limits;

        public Image getImage() {
            return image;
        }

        public void setImage(Image image) {
            this.image = image;
        }

        public List<String> getAllowed_file_extensions() {
            return allowed_file_extensions;
        }

        public void setAllowed_file_extensions(List<String> allowed_file_extensions) {
            this.allowed_file_extensions = allowed_file_extensions;
        }

        public List<String> getAllowed_file_types() {
            return allowed_file_types;
        }

        public void setAllowed_file_types(List<String> allowed_file_types) {
            this.allowed_file_types = allowed_file_types;
        }

        public List<String> getAllowed_file_upload_methods() {
            return allowed_file_upload_methods;
        }

        public void setAllowed_file_upload_methods(List<String> allowed_file_upload_methods) {
            this.allowed_file_upload_methods = allowed_file_upload_methods;
        }

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public int getNumber_limits() {
            return number_limits;
        }

        public void setNumber_limits(int number_limits) {
            this.number_limits = number_limits;
        }

        protected static class Image {

            String detail;
            int number_limits;
            List<String> transfer_methods;
            boolean enabled = false;

            public String getDetail() {
                return detail;
            }

            public void setDetail(String detail) {
                this.detail = detail;
            }

            public int getNumber_limits() {
                return number_limits;
            }

            public void setNumber_limits(int number_limits) {
                this.number_limits = number_limits;
            }

            public List<String> getTransfer_methods() {
                return transfer_methods;
            }

            public void setTransfer_methods(List<String> transfer_methods) {
                this.transfer_methods = transfer_methods;
            }

            public boolean isEnabled() {
                return enabled;
            }

            public void setEnabled(boolean enabled) {
                this.enabled = enabled;
            }

        }

    }

    protected static class Model {

        Map<String, Object> completion_params = new HashMap<>();
        String mode = "chat";
        String name;
        String provider;

        public Model(String model, String provider) {
            this.name = model;
            this.provider = provider;
        }

        public Map<String, Object> getCompletion_params() {
            return completion_params;
        }

        public void setCompletion_params(Map<String, Object> completion_params) {
            this.completion_params = completion_params;
        }

        public String getMode() {
            return mode;
        }

        public void setMode(String mode) {
            this.mode = mode;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getProvider() {
            return provider;
        }

        public void setProvider(String provider) {
            this.provider = provider;
        }

    }

    protected static class MoreLikeThis {

        boolean enabled = false;

        public boolean getEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

    }

    protected static class RetrieverResource {

        boolean enabled;

        public boolean getEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }
    }

    protected static class SensitiveWordAvoidance {

        List<Object> configs;
        String type;
        boolean enabled = false;

        public List<Object> getConfigs() {
            return configs;
        }

        public void setConfigs(List<Object> configs) {
            this.configs = configs;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

    }

    protected static class SpeechToText {

        boolean enabled = false;

        public boolean getEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

    }

    protected static class SuggestedQuestionsAfterAnswer {

        boolean enabled = false;

        public boolean getEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

    }

    protected static class TextToSpeech {

        boolean enabled = false;

        public boolean getEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

    }

}