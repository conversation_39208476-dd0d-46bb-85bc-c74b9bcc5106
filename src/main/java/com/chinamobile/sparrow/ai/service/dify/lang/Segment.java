package com.chinamobile.sparrow.ai.service.dify.lang;

import java.util.List;

public class Segment {

    Detail segment;
    double score;

    public Detail getSegment() {
        return segment;
    }

    public void setSegment(Detail segment) {
        this.segment = segment;
    }

    public double getScore() {
        return score;
    }

    public void setScore(double score) {
        this.score = score;
    }


    public static class Detail {

        String id;
        int position;
        String documentId;
        String content;
        Object answer;
        int wordCount;
        int tokens;
        List<String> keywords;
        String indexNodeId;
        String indexNodeHash;
        int hitCount;
        boolean enabled;
        Object disabledAt;
        Object disabledBy;
        String status;
        String createdBy;
        long createdAt;
        long indexingAt;
        long completedAt;
        Object error;
        Object stoppedAt;
        Document document;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public int getPosition() {
            return position;
        }

        public void setPosition(int position) {
            this.position = position;
        }

        public String getDocumentId() {
            return documentId;
        }

        public void setDocumentId(String documentId) {
            this.documentId = documentId;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public Object getAnswer() {
            return answer;
        }

        public void setAnswer(Object answer) {
            this.answer = answer;
        }

        public int getWordCount() {
            return wordCount;
        }

        public void setWordCount(int wordCount) {
            this.wordCount = wordCount;
        }

        public int getTokens() {
            return tokens;
        }

        public void setTokens(int tokens) {
            this.tokens = tokens;
        }

        public List<String> getKeywords() {
            return keywords;
        }

        public void setKeywords(List<String> keywords) {
            this.keywords = keywords;
        }

        public String getIndexNodeId() {
            return indexNodeId;
        }

        public void setIndexNodeId(String indexNodeId) {
            this.indexNodeId = indexNodeId;
        }

        public String getIndexNodeHash() {
            return indexNodeHash;
        }

        public void setIndexNodeHash(String indexNodeHash) {
            this.indexNodeHash = indexNodeHash;
        }

        public int getHitCount() {
            return hitCount;
        }

        public void setHitCount(int hitCount) {
            this.hitCount = hitCount;
        }

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public Object getDisabledAt() {
            return disabledAt;
        }

        public void setDisabledAt(Object disabledAt) {
            this.disabledAt = disabledAt;
        }

        public Object getDisabledBy() {
            return disabledBy;
        }

        public void setDisabledBy(Object disabledBy) {
            this.disabledBy = disabledBy;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getCreatedBy() {
            return createdBy;
        }

        public void setCreatedBy(String createdBy) {
            this.createdBy = createdBy;
        }

        public long getCreatedAt() {
            return createdAt;
        }

        public void setCreatedAt(long createdAt) {
            this.createdAt = createdAt;
        }

        public long getIndexingAt() {
            return indexingAt;
        }

        public void setIndexingAt(long indexingAt) {
            this.indexingAt = indexingAt;
        }

        public long getCompletedAt() {
            return completedAt;
        }

        public void setCompletedAt(long completedAt) {
            this.completedAt = completedAt;
        }

        public Object getError() {
            return error;
        }

        public void setError(Object error) {
            this.error = error;
        }

        public Object getStoppedAt() {
            return stoppedAt;
        }

        public void setStoppedAt(Object stoppedAt) {
            this.stoppedAt = stoppedAt;
        }

        public Document getDocument() {
            return document;
        }

        public void setDocument(Document document) {
            this.document = document;
        }
    }

    public static class Document {

        String id;
        String dataSourceType;
        String name;
        Object docType;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getDataSourceType() {
            return dataSourceType;
        }

        public void setDataSourceType(String dataSourceType) {
            this.dataSourceType = dataSourceType;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Object getDocType() {
            return docType;
        }

        public void setDocType(Object docType) {
            this.docType = docType;
        }

    }

}