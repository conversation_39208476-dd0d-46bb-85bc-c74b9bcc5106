package com.chinamobile.sparrow.ai.service.dify.infra;

import com.chinamobile.sparrow.domain.util.HttpUtil;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import org.springframework.http.HttpHeaders;

import java.util.concurrent.TimeUnit;

import static com.chinamobile.sparrow.domain.util.HttpUtil.createUnsafeSSLContext;

public class V1ApiBase extends ApiBase {


    public V1ApiBase(String baseUrl, String apiKey, ConnectionPool connectionPool) {
        super(baseUrl);

        this.client = new OkHttpClient.Builder()
                .connectionPool(connectionPool == null ? new ConnectionPool() : connectionPool)
                .readTimeout(10, TimeUnit.MINUTES)
                .sslSocketFactory(createUnsafeSSLContext().getSocketFactory(), new HttpUtil.TrustAllCertsManager())
                .hostnameVerifier(new HttpUtil.TrustAllHostnameVerifier())
                .addInterceptor(chain -> {
                    Request.Builder _builder = chain.request().newBuilder()
                            .addHeader(HttpHeaders.CONTENT_TYPE, org.springframework.http.MediaType.APPLICATION_JSON_VALUE)
                            .addHeader(HttpHeaders.AUTHORIZATION, String.format("Bearer %s", apiKey));

                    return chain.proceed(_builder.build());
                })
                .build();
    }

}