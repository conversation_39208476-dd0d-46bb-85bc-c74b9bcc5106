package com.chinamobile.sparrow.ai.repository.llm;

import com.agentsflex.core.llm.BaseLlm;
import com.agentsflex.core.llm.LlmConfig;
import com.agentsflex.core.llm.embedding.EmbeddingModel;
import com.agentsflex.llm.deepseek.DeepseekConfig;
import com.agentsflex.llm.deepseek.DeepseekLlm;
import com.agentsflex.llm.openai.OpenAILlm;
import com.agentsflex.llm.openai.OpenAILlmConfig;
import com.agentsflex.llm.qwen.QwenLlm;
import com.agentsflex.llm.qwen.QwenLlmConfig;
import com.chinamobile.sparrow.ai.infra.llm.embedding.DefaultEmbeddingModel;
import com.chinamobile.sparrow.ai.model.llm.Model;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PaginatedRecords;
import com.chinamobile.sparrow.domain.lang.Sorter;
import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import okhttp3.ConnectionPool;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Tuple3;
import org.springframework.aop.framework.AopContext;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.BiFunction;

public class ModelRepository<T extends Model> extends AbstractEntityRepository<T> {

    final boolean debug;
    final List<Tuple3<String, String, BaseLlm<? extends LlmConfig>>> languageModels;
    final List<Tuple3<String, String, EmbeddingModel>> embeddingModels;

    public ModelRepository(EntityManagerFactory entityManagerFactory, JinqJPAStreamProvider jinqJPAStreamProvider, Class<T> tClass, boolean debug) {
        super(entityManagerFactory, jinqJPAStreamProvider, tClass);
        this.debug = debug;
        this.languageModels = new CopyOnWriteArrayList<>();
        this.embeddingModels = new CopyOnWriteArrayList<>();
    }

    @Override
    protected <V extends Comparable<V>> JinqStream<T> sort(JinqStream<T> query, BiFunction<JinqStream<T>, JinqStream.CollectComparable<T, V>, JinqStream<T>> compare, String field) {
        switch (field) {
            case "createTime":
                return compare.apply(query, i -> (V) i.getCreateTime());
            case "title":
                return compare.apply(query, i -> (V) i.getTitle());
            case "name":
                return compare.apply(query, i -> (V) i.getName());
            case "baseURL":
                return compare.apply(query, i -> (V) i.getBaseURL());
            case "aptType":
                return compare.apply(query, i -> (V) i.getApiType());
            case "mode":
                return compare.apply(query, i -> (V) i.getMode());
            default:
                return query;
        }
    }

    @Transactional(readOnly = true)
    public Result<T> get(String id) {
        Result<T> _record = new Result<>();

        _record.data = getCurrentSession().get(tClass, id);
        if (_record.data == null) {
            _record.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{tClass.getSimpleName()});
        }

        return _record;
    }

    @Transactional(readOnly = true)
    public List<T> search(String title, Model.ENUM_MODE mode, List<Sorter> sorters) {
        return search(-1, -1, sorters, title, mode).records;
    }

    @Transactional(readOnly = true)
    public PaginatedRecords<T> search(int count, int index, List<Sorter> sorters, String title, Model.ENUM_MODE mode) {
        JinqStream<T> _query = stream(tClass);

        if (StringUtils.hasLength(title)) {
            _query = _query.where(i -> i.getTitle() != null && i.getTitle().contains(title));
        }

        if (mode != null) {
            _query = _query.where(i -> i.getMode() == mode);
        }

        if (CollectionUtils.isEmpty(sorters)) {
            _query = _query.sortedBy(Model::getTitle).sortedDescendingBy(AbstractEntity::getCreateTime);
        } else {
            _query = sort(_query, sorters);
        }

        PaginatedRecords<T> _page = new PaginatedRecords<>(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip((long) count * index).limit(count);
        }
        _page.records = _query.toList();

        return _page;
    }

    public Result<String> save(T record, String operatorId) throws InstantiationException, IllegalAccessException {
        Result<String> _id = new Result<>();

        boolean _alreadyExisted = true;

        Result<T> _record = ((ModelRepository<T>) AopContext.currentProxy()).get(record.getId());
        if (!_record.isOK()) {
            _record.data = tClass.newInstance();

            _alreadyExisted = false;
        }

        String _identifier = record.getId();
        String _name = record.getName();
        if (stream(tClass).where(i -> !_identifier.equals(i.getId()) && _name.equals(i.getName()))
                .findFirst().isPresent()) {
            _id.setCode(Result.DATABASE_RECORD_ALREADY_EXIST);
            return _id;
        }

        ((ModelRepository<T>) AopContext.currentProxy()).copyProperties(record, _record.data, null);

        Result<Void> _success = _alreadyExisted ? update(_record.data, operatorId) : add(_record.data, operatorId);
        if (!_success.isOK()) {
            return _id.pack(_success);
        }

        ((ModelRepository<T>) AopContext.currentProxy()).cacheLanguageModels();

        _id.data = _record.data.getId();
        return _id;
    }

    public Result<Void> remove(String id, String operatorId) {
        Result<Void> _success = new Result<>();

        Result<T> _record = ((ModelRepository<T>) AopContext.currentProxy()).get(id);
        if (!_record.isOK()) {
            return _success.pack(_record);
        }

        getCurrentSession().remove(_record.data);

        ((ModelRepository<T>) AopContext.currentProxy()).cacheLanguageModels();

        return _success;
    }

    public List<Tuple3<String, String, BaseLlm<? extends LlmConfig>>> languageModels() {
        if (languageModels.isEmpty()) {
            ((ModelRepository<T>) AopContext.currentProxy()).cacheLanguageModels();
        }

        return languageModels;
    }

    @Transactional(readOnly = true)
    public void cacheLanguageModels() {
        getCurrentSession().flush();

        Model.ENUM_MODE _embedding = Model.ENUM_MODE.EMBEDDING;
        List<T> _records = stream(tClass).where(i -> i.getMode() != _embedding)
                .toList();

        languageModels.clear();
        _records.forEach(i -> {
            LlmConfig _config;
            BaseLlm<? extends LlmConfig> _model;

            switch (i.getApiType()) {
                // 通义千问系列模型
                case QWEN:
                    _config = new QwenLlmConfig();
                    _config.setEndpoint(i.getBaseURL());
                    _config.setModel(i.getName());
                    _config.setApiKey(i.getApiKey());
                    _config.setDebug(debug);

                    _model = new QwenLlm((QwenLlmConfig) _config);
                    break;
                // 深度搜索系列模型
                case DEEP_SEEK:
                    _config = new DeepseekConfig();
                    _config.setEndpoint(i.getBaseURL());
                    _config.setModel(i.getName());
                    _config.setApiKey(i.getApiKey());
                    _config.setDebug(debug);

                    _model = new DeepseekLlm((DeepseekConfig) _config);
                    break;
                // 其他OpenAI兼容模型
                default:
                    _config = new OpenAILlmConfig();
                    _config.setEndpoint(i.getBaseURL());
                    _config.setModel(i.getName());
                    _config.setApiKey(i.getApiKey());
                    _config.setDebug(debug);

                    _model = new OpenAILlm((OpenAILlmConfig) _config);
            }

            languageModels.add(new Tuple3<>(i.getName(), i.getProvider(), _model));
        });
    }

    public List<Tuple3<String, String, EmbeddingModel>> embeddingModels() {
        return embeddingModels;
    }

    @Transactional(readOnly = true)
    public void cacheEmbeddingModels(ConnectionPool connectionPool) {
        getCurrentSession().flush();

        Model.ENUM_MODE _embedding = Model.ENUM_MODE.EMBEDDING;
        List<T> _records = stream(tClass).where(i -> i.getMode() == _embedding)
                .toList();

        embeddingModels.clear();
        _records.forEach(i -> {
            DefaultEmbeddingModel _model = new DefaultEmbeddingModel(i.getBaseURL(), i.getApiKey(), i.getName(), connectionPool);
            embeddingModels.add(new Tuple3<>(i.getName(), i.getProvider(), _model));
        });
    }

}