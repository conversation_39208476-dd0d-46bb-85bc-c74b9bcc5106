package com.chinamobile.sparrow.ai.repository.llm;

import com.agentsflex.core.message.Message;
import com.chinamobile.sparrow.ai.infra.llm.memory.ChatMemory;
import com.chinamobile.sparrow.ai.infra.llm.memory.DefaultChatMemory;
import com.chinamobile.sparrow.ai.model.llm.Conversation;
import com.chinamobile.sparrow.ai.model.llm.Memory;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MemoryRepository<T extends Memory> extends AbstractMemoryRepository<T> {


    public MemoryRepository(EntityManagerFactory entityManagerFactory, JinqJPAStreamProvider jinqJPAStreamProvider, Class<T> tClass, Integer memorySize, ConversationRepository<? extends Conversation> conversationRepository, AbstractMediaRepository mediaRepository) {
        super(entityManagerFactory, jinqJPAStreamProvider, tClass, memorySize, conversationRepository, mediaRepository);
    }

    @Override
    public ChatMemory getChatMemory(String conversationId) {
        return StringUtils.hasLength(conversationId) ? new DefaultChatMemory() : new DefaultChatMemory(conversationId);
    }

    @Override
    public Map<String, List<Message>> getStorages() {
        Map<String, List<Message>> _storages = new HashMap<>();
        memories.forEach(i -> _storages.put(i.id().toString(), i.getMessages()));
        return _storages;
    }

}