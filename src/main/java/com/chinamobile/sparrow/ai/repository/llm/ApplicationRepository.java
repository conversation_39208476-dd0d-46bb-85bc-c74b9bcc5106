package com.chinamobile.sparrow.ai.repository.llm;

import com.chinamobile.sparrow.ai.model.llm.Application;
import com.chinamobile.sparrow.ai.service.dify.ConsoleFacade;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PaginatedRecords;
import com.chinamobile.sparrow.domain.lang.ResultWarpperRuntimeException;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.springframework.aop.framework.AopContext;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

public class ApplicationRepository<T extends Application> extends AbstractEntityRepository<T> {

    protected final ConsoleFacade consoleFacade;

    public ApplicationRepository(EntityManagerFactory entityManagerFactory, JinqJPAStreamProvider jinqJPAStreamProvider, Class<T> tClass, ConsoleFacade consoleFacade) {
        super(entityManagerFactory, jinqJPAStreamProvider, tClass);
        this.consoleFacade = consoleFacade;
    }

    @Transactional(readOnly = true)
    public Result<T> get(String id) {
        Result<T> _record = new Result<>();

        _record.data = getCurrentSession().get(tClass, id);
        if (_record.data == null) {
            _record.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{tClass.getSimpleName()});
        } else {
            parseApplication(Collections.singletonList(_record.data));
        }

        return _record;
    }

    @Transactional(readOnly = true)
    public Result<T> permit(String id, String userId) {
        Result<T> _record = get(id);
        if (!_record.isOK()) {
            return _record;
        }

        if (!Objects.equals(userId, _record.data.getCreatorId())) {
            _record.setCode(Result.DATA_ACCESS_DENY);
        }

        return _record;
    }

    @Transactional(readOnly = true)
    public PaginatedRecords<T> search(int count, int index, String name) {
        JinqStream<T> _query = stream(tClass);

        if (StringUtils.hasLength(name)) {
            _query = _query.where(i -> i.getName() != null && i.getName().contains(name));
        }

        PaginatedRecords<T> _page = new PaginatedRecords<>(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip((long) count * index).limit(count);
        }
        _page.records = _query.toList();

        parseApplication(_page.records);

        return _page;
    }

    public List<T> me(String userId) {
        List<T> _records = stream(tClass).where(i -> userId.equals(i.getCreatorId())).toList();
        parseApplication(_records);
        return _records;
    }

    public Result<String> save(T record, String operatorId) throws InstantiationException, IllegalAccessException {
        Result<String> _id = new Result<>();

        String _platformId = record.getPlatformId();
        Application.ENUM_PLATFORM _platform = record.getPlatform();
        T _record = stream(tClass).where(i -> _platform == i.getPlatform() && _platformId.equals(i.getPlatformId()))
                .findFirst().orElse(null);
        if (_record == null) {
            _record = tClass.newInstance();
            _record.setPlatform(_platform);
            _record.setPlatformId(_platformId);
            add(_record, operatorId);
        }

        _id.data = _record.getId();
        return _id;
    }

    public Result<Void> remove(String id, String operatorId) {
        Result<Void> _success = new Result<>();

        Result<T> _record = ((ApplicationRepository<T>) AopContext.currentProxy()).permit(id, operatorId);
        if (!_record.isOK()) {
            return _success.pack(_record);
        }

        // 删除记录
        getCurrentSession().remove(_record.data);

        if (Application.ENUM_PLATFORM.DIFY == _record.data.getPlatform() && StringUtils.hasLength(_record.data.getPlatformId())) {
            _success = consoleFacade.removeApp(_record.data.getPlatformId());
            if (!_success.isOK()) {
                throw new ResultWarpperRuntimeException(_success);
            }
        }

        return _success;
    }

    public Result<String> getInstallId(String difyId) {
        Result<String> _installId = new Result<>();

        Result<List<ConsoleFacade.Application>> _applications = consoleFacade.installApps();
        if (!_applications.isOK()) {
            return _installId.pack(_applications);
        }

        _installId.data = _applications.data.stream()
                .filter(i -> Objects.equals(i.getId(), difyId))
                .map(ConsoleFacade.Application::getInstallId)
                .findFirst().orElse(null);
        return _installId;
    }

    protected void parseApplication(List<T> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        Long _total = null;
        int _index = 0;
        int _count = 0;

        List<ConsoleFacade.Application> _applications = new ArrayList<>();
        while (_total == null || _count < _total) {
            Result<PaginatedRecords<ConsoleFacade.Application>> _temp = consoleFacade.searchApps(null, _index, null);
            if (!_temp.isOK()) {
                break;
            }

            _total = _temp.data.total;
            _index++;
            _count += _temp.data.records.size();

            _applications.addAll(_temp.data.records);
        }

        records.stream()
                .filter(i -> Application.ENUM_PLATFORM.DIFY == i.getPlatform() && StringUtils.hasLength(i.getPlatformId()))
                .forEach(i -> {
                    ConsoleFacade.Application _application = _applications.stream()
                            .filter(j -> Objects.equals(i.getPlatformId(), j.getId()))
                            .findFirst().orElse(null);
                    if (_application != null) {
                        i.setIcon(StringUtils.hasLength(_application.getIcon_url()) ? _application.getIcon_url() : _application.getIcon());
                        i.setName(_application.getName());
                        i.setDescription(_application.getDescription());
                    }
                });
    }

}