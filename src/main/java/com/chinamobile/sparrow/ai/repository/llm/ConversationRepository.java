package com.chinamobile.sparrow.ai.repository.llm;

import com.agentsflex.core.llm.BaseLlm;
import com.agentsflex.core.llm.ChatContext;
import com.agentsflex.core.llm.LlmConfig;
import com.agentsflex.core.llm.StreamResponseListener;
import com.agentsflex.core.llm.response.AiMessageResponse;
import com.agentsflex.core.message.HumanMessage;
import com.agentsflex.core.message.Message;
import com.agentsflex.core.message.MessageStatus;
import com.agentsflex.core.message.SystemMessage;
import com.agentsflex.core.prompt.HistoriesPrompt;
import com.agentsflex.core.prompt.ImagePrompt;
import com.alibaba.fastjson.JSONPath;
import com.chinamobile.sparrow.ai.infra.llm.memory.ChatMemory;
import com.chinamobile.sparrow.ai.infra.llm.plugin.IPlugin;
import com.chinamobile.sparrow.ai.infra.llm.plugin.ParserPlugin;
import com.chinamobile.sparrow.ai.infra.llm.plugin.WebSearchPlugin;
import com.chinamobile.sparrow.ai.model.llm.*;
import com.chinamobile.sparrow.ai.service.dify.ConsoleFacade;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.model.media.Media;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import com.chinamobile.sparrow.domain.service.ICacheService;
import com.chinamobile.sparrow.domain.service.search.IWebSearchService;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonObject;
import okhttp3.Response;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.tuples.Tuple3;
import org.springframework.aop.framework.AopContext;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.persistence.EntityManagerFactory;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

public class ConversationRepository<T extends Conversation> extends AbstractEntityRepository<T> {

    public static final String MESSAGE_METADATA_ID = "id";
    public static final String MESSAGE_METADATA_MEDIA_ID = "mediaId";
    public static final String MESSAGE_METADATA_USER = "userId";
    public static final String MESSAGE_METADATA_TIMESTAMP = "timestamp";
    public static final String MESSAGE_METADATA_REASONING = "reasoning";
    public static final String MESSAGE_METADATA_REASONING_DURATION = "duration";

    protected final String conversationKeyTemplate;

    protected final String multimodalModel;
    protected final String namingModel;
    protected final Integer memoryMaxSize;
    protected final String retrievalModel;
    protected final Integer retrievalTopK;
    protected final Double retrievalScore;
    protected final String rerankingModelName;
    protected final String rerankingProviderName;
    protected final Double keywordWeight;
    protected final Double vectorWeight;
    protected final ModelRepository modelRepository;
    protected final AbstractMemoryRepository<? extends Memory> memoryRepository;
    protected final DatasetRepository<? extends Dataset> datasetRepository;
    protected final AbstractMediaRepository mediaRepository;
    protected final List<IPlugin> pluginImpls;
    protected final ConsoleFacade consoleFacade;
    protected final ICacheService cacheService;
    protected final ApplicationEventPublisher eventPublisher;

    public ConversationRepository(EntityManagerFactory entityManagerFactory, JinqJPAStreamProvider jinqJPAStreamProvider, Class<T> tClass, String conversationKeyTemplate, String multimodalModel, String namingModel, Integer memoryMaxSize, String retrievalModel, Integer retrievalTopK, Double retrievalScore, String rerankingModelName, String rerankingProviderName, Double keywordWeight, Double vectorWeight, ModelRepository modelRepository, AbstractMemoryRepository<? extends Memory> memoryRepository, DatasetRepository<? extends Dataset> datasetRepository, AbstractMediaRepository mediaRepository, List<IPlugin> plugins, ConsoleFacade consoleFacade, ICacheService cacheService, ApplicationEventPublisher eventPublisher) {
        super(entityManagerFactory, jinqJPAStreamProvider, tClass);
        this.conversationKeyTemplate = conversationKeyTemplate;
        this.multimodalModel = multimodalModel;
        this.namingModel = namingModel;
        this.memoryMaxSize = memoryMaxSize;
        this.retrievalModel = retrievalModel;
        this.retrievalTopK = retrievalTopK;
        this.retrievalScore = retrievalScore;
        this.rerankingModelName = rerankingModelName;
        this.rerankingProviderName = rerankingProviderName;
        this.keywordWeight = keywordWeight;
        this.vectorWeight = vectorWeight;
        this.modelRepository = modelRepository;
        this.memoryRepository = memoryRepository;
        this.datasetRepository = datasetRepository;
        this.mediaRepository = mediaRepository;
        this.pluginImpls = plugins;
        this.consoleFacade = consoleFacade;
        this.cacheService = cacheService;
        this.eventPublisher = eventPublisher;
    }

    @Transactional(readOnly = true)
    public Result<T> get(String id) {
        Result<T> _record = new Result<>();

        _record.data = getCurrentSession().get(tClass, id);
        if (_record.data == null) {
            _record.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{tClass.getSimpleName()});
        }

        return _record;
    }

    @Transactional(readOnly = true)
    public Result<T> permit(String id, String userId) {
        Result<T> _record = get(id);
        if (!_record.isOK()) {
            return _record;
        }

        if (!Objects.equals(userId, _record.data.getCreatorId())) {
            _record.setCode(Result.DATA_ACCESS_DENY);
        }

        return _record;
    }

    @Transactional(readOnly = true)
    public Result<T> permitFromCache(String id, String userId) {
        Result<T> _record = new Result<>();

        String _key = String.format(conversationKeyTemplate, id, userId);
        _record.data = cacheService.get(_key, tClass);
        if (_record.data != null) {
            return _record;
        }

        _record = permit(id, userId);
        if (!_record.isOK()) {
            return _record;
        }

        cacheService.set(_key, _record.data);

        return _record;
    }

    @Transactional(readOnly = true)
    public boolean accessible(String id, String userId) {
        T _record = stream(tClass).where(i -> id.equals(i.getId()))
                .findFirst().orElse(null);

        return _record == null || Objects.equals(userId, _record.getCreatorId());
    }

    public List<T> me(String userId) {
        return stream(tClass).where(i -> userId.equals(i.getCreatorId())).toList();
    }

    public Result<String> addOrDefault(String originId, String operatorId) throws InstantiationException, IllegalAccessException {
        Result<String> _id = new Result<>();
        if (StringUtils.hasLength(originId) && memoryRepository.find(1, originId).isEmpty()) {
            _id.data = originId;
            return _id;
        }

        HistoriesPrompt _prompt = memoryRepository.prompt(null);

        T _record = tClass.newInstance();
        _record.setId(_prompt.getMemory().id().toString());
        super.add(_record, operatorId);

        _id.data = _record.getId();
        return _id;
    }

    @EventListener
    @Async
    public void updateDifyId(UpdateConversationIdEvent event) {
        Result<T> _record = permit(event.id, event.userId);

        if (_record.isOK()) {
            _record.data.setDifyId(event.difyId);
            update(_record.data, event.userId);
        }
    }

    public Result<String> rename(String id, String title, String operatorId) {
        Result<String> _title = new Result<>();

        Result<T> _record = ((ConversationRepository) AopContext.currentProxy()).permit(id, operatorId);
        if (!_record.isOK()) {
            return _title.pack(_record);
        }

        if (!StringUtils.hasLength(title)) {
            List<Message> _messages = ((ChatMemory) memoryRepository.prompt(id).getMemory()).getMessages(2);

            HistoriesPrompt _prompt = new HistoriesPrompt();
            _prompt.addMessages(_messages);
            _prompt.addMessage(new HumanMessage("请根据上述对话内容生成一个简短、具体且有吸引力的标题，不超过15个字。\n要求：\n1. 标题应准确反映对话的核心主题或问题。\n2. 请直接返回标题，禁止多余的内容"));

            BaseLlm<? extends LlmConfig> _languageModel = (BaseLlm<? extends LlmConfig>) modelRepository.languageModels().stream()
                    .filter(i -> Objects.equals(namingModel, ((Tuple3<String, String, BaseLlm<? extends LlmConfig>>) i).getOne()))
                    .map(i -> ((Tuple3<String, String, BaseLlm<? extends LlmConfig>>) i).getThree())
                    .findFirst().orElse(null);
            if (_languageModel != null) {
                AiMessageResponse _response = _languageModel.chat(_prompt);
                title = _response.getMessage().getContent();
            }
        }

        _record.data.setTitle(title);
        update(_record.data, operatorId);

        _title.data = title;
        return _title;
    }

    public Result<Void> remove(String id, String operatorId) {
        Result<Void> _success = new Result<>();

        Result<T> _record = ((ConversationRepository<T>) AopContext.currentProxy()).permit(id, operatorId);
        if (!_record.isOK()) {
            return _success.pack(_record);
        }

        // 删除记忆
        memoryRepository.remove(id, operatorId);

        // 删除对话
        getCurrentSession().remove(_record.data);

        return _success;
    }

    public SseEmitter generate(String id, String model, List<String> plugins, Integer memorySize, String message, String mediaId, String operatorId) {
        return generate((params, sse) -> {
            HistoriesPrompt _prompt = memoryRepository.prompt(id);

            int _memorySize = memorySize == null ? memoryMaxSize : memorySize;
            _prompt.setMaxAttachedMessageCount(_memorySize % 2 == 0 ? _memorySize + 1 : _memorySize);

            String _model = !StringUtils.hasLength(mediaId) && _prompt.getMemory().getMessages().stream().noneMatch(i -> i instanceof ImagePrompt.TextAndImageMessage)
                    ? model
                    : multimodalModel;
            BaseLlm<? extends LlmConfig> _languageModel = (BaseLlm<? extends LlmConfig>) modelRepository.languageModels().stream()
                    .filter(i -> Objects.equals(((Tuple3<String, String, BaseLlm<? extends LlmConfig>>) i).getOne(), _model))
                    .map(i -> ((Tuple3<String, String, BaseLlm<? extends LlmConfig>>) i).getThree())
                    .findFirst().orElse(null);
            if (_languageModel == null) {
                sse.completeWithError(new IllegalArgumentException());
                return;
            }

            if (StringUtils.hasLength(params.prompt)) {
                _prompt.setSystemMessage(new SystemMessage(params.prompt));
            }

            HumanMessage _message;
            if (StringUtils.hasLength(mediaId)) {
                _message = new ImagePrompt.TextAndImageMessage(new ImagePrompt(message, mediaId));
                _message.addMetadata(MESSAGE_METADATA_MEDIA_ID, mediaId);
                _prompt.addMessage(_message);
            } else {
                _message = new HumanMessage(message);
            }

            _message.addMetadata(MESSAGE_METADATA_ID, memoryRepository.id());
            _message.addMetadata(MESSAGE_METADATA_USER, operatorId);
            _message.addMetadata(MESSAGE_METADATA_TIMESTAMP, new Date());
            _prompt.addMessage(_message);

            final Boolean[] _reasoningField = new Boolean[]{null};
            final String[] _reasoning = new String[]{""};
            final Date[] _start = new Date[]{null};
            final Long[] _duration = new Long[]{null};

            _languageModel.chatStream(_prompt, new StreamResponseListener() {
                public void onStart(ChatContext context) {
                    if (!params.links.isEmpty()) {
                        try {
                            ResponseMessage _message = new ResponseMessage();
                            _message.links = params.links;
                            sse.send(_message);
                        } catch (IOException e) {
                            sse.completeWithError(e);
                        }
                    }

                }

                public void onMessage(ChatContext context, AiMessageResponse message) {
                    try {
                        ResponseMessage _message = new ResponseMessage();

                        if (StringUtils.hasLength(message.getMessage().getReasoningContent())) {
                            if (_reasoningField[0] == null) {
                                _reasoningField[0] = true;
                            }

                            if (_start[0] == null) {
                                _start[0] = new Date();
                            }

                            _reasoning[0] += message.getMessage().getReasoningContent();

                            _message.reasoning = new Memory.Reasoning();
                            _message.reasoning.setDescription(message.getMessage().getReasoningContent());
                            sse.send(_message);
                        } else {
                            if (_reasoningField[0] == null) {
                                _reasoningField[0] = false;
                            }

                            if (_reasoningField[0] && _duration[0] == null) {
                                _duration[0] = new Date().getTime() - _start[0].getTime();

                                _message.reasoning = new Memory.Reasoning();
                                _message.reasoning.setDuration(_duration[0]);
                                sse.send(_message);
                            } else {
                                String _text = message.getMessage().getContent();
                                if (StringUtils.hasLength(_text)) {
                                    String _fullText = message.getMessage().getMessageContent().toString();

                                    // 开始思考
                                    if (_start[0] == null && StringUtils.trimWhitespace(_text).equals("<think>") && _text.equals(_fullText)) {
                                        _start[0] = new Date();
                                    }
                                    // 思考中
                                    else if (_start[0] != null && _duration[0] == null) {
                                        // 结束思考
                                        if (StringUtils.trimWhitespace(_text).equals("</think>")) {
                                            _duration[0] = (new Date()).getTime() - _start[0].getTime();

                                            _message.reasoning = new Memory.Reasoning();
                                            _message.reasoning.setDuration(_duration[0]);
                                            sse.send(_message);
                                        }
                                        // 思考中
                                        else {
                                            _reasoning[0] += _text;

                                            _message.reasoning = new Memory.Reasoning();
                                            _message.reasoning.setDescription(_text);
                                            sse.send(_message);
                                        }
                                    } else {
                                        _message.setText(_text);
                                        sse.send(_message);
                                    }
                                }
                            }
                        }

                        if (MessageStatus.END.equals(message.getMessage().getStatus())) {
                            if (StringUtils.hasLength(_reasoning[0])) {
                                message.getMessage().addMetadata(MESSAGE_METADATA_REASONING, _reasoning[0]);
                                message.getMessage().addMetadata(MESSAGE_METADATA_REASONING_DURATION, _duration[0]);
                            }

                            message.getMessage().addMetadata(MESSAGE_METADATA_ID, memoryRepository.id());
                            message.getMessage().addMetadata(MESSAGE_METADATA_TIMESTAMP, new Date());
                        }
                    } catch (IOException e) {
                        sse.completeWithError(e);
                    }

                }

                public void onStop(ChatContext context) {
                    sse.complete();
                }

                public void onFailure(ChatContext context, Throwable throwable) {
                    sse.completeWithError(throwable);
                }
            });
        }, id, plugins, message, operatorId);
    }

    public SseEmitter generateByDify(String id, String difyConversationId, String appId, List<String> datasetIds, String model, List<String> plugins, String message, String operatorId) {
        return generate((params, sse) -> {
            String _conversationId = StringUtils.hasLength(difyConversationId) ? difyConversationId : params.difyId;

            String _provider = (String) modelRepository.languageModels().stream()
                    .filter(i -> Objects.equals(((Tuple3<String, String, BaseLlm<? extends LlmConfig>>) i).getOne(), model))
                    .map(i -> ((Tuple3<String, String, BaseLlm<? extends LlmConfig>>) i).getTwo())
                    .findFirst().orElse(null);

            final Date[] _start = new Date[]{null};
            final Long[] _duration = new Long[]{null};

            EventSourceListener _listener = new EventSourceListener() {

                public void onOpen(@NotNull EventSource eventSource, @NotNull Response response) {
                    if (!params.links.isEmpty()) {
                        try {
                            ResponseMessage _message = new ResponseMessage();
                            _message.links = params.links;
                            sse.send(_message);
                        } catch (IOException e) {
                            sse.completeWithError(e);
                        }
                    }

                }

                public void onEvent(@NotNull EventSource eventSource, String eventId, String type, @NotNull String data) {
                    if (!StringUtils.hasLength(_conversationId)) {
                        String _id = JSONPath.read(data, "$.conversation_id", String.class);
                        eventPublisher.publishEvent(new UpdateConversationIdEvent(id, _id, operatorId));
                    }

                    JsonObject _json = ConverterUtil.json2Object(data, JsonObject.class);

                    ResponseMessage _message = new ResponseMessage();

                    switch ((String) JSONPath.read(data, "$.event", String.class)) {
                        case "message":
                            String _text = _json.get("answer").getAsString();
                            if (_start[0] == null && StringUtils.trimWhitespace(_text).equals("<think>")) {
                                _start[0] = new Date();
                            } else {
                                if (_start[0] != null && _duration[0] == null && StringUtils.trimWhitespace(_text).equals("</think>")) {
                                    _duration[0] = new Date().getTime() - _start[0].getTime();

                                    _message.reasoning = new Memory.Reasoning();
                                    _message.reasoning.setDuration(_duration[0]);
                                } else if (_start[0] != null && _duration[0] == null) {
                                    _message.reasoning = new Memory.Reasoning();
                                    _message.reasoning.setDescription(_text);
                                } else {
                                    _message.setText(_text);
                                }

                                try {
                                    sse.send(_message);
                                } catch (IOException e) {
                                    sse.completeWithError(e);
                                }
                            }

                            break;
                        case "message_end":
                            if (JSONPath.contains(data, "$.metadata.retriever_resources")) {
                                String _temp = _json.get("metadata").getAsJsonObject().get("retriever_resources").toString();
                                List<JsonObject> _resources = ConverterUtil.json2Object(_temp, (new TypeToken<List<JsonObject>>() {
                                }).getType());

                                List<Segment> _segments = _resources.stream()
                                        .map(i -> {
                                            Segment _segment = new Segment();
                                            _segment.setId(i.get("document_id").getAsString());
                                            _segment.setName(i.get("document_name").getAsString());
                                            _segment.setContent(i.get("content").getAsString());
                                            _segment.setScore(i.get("score").getAsDouble());

                                            return _segment;
                                        })
                                        .collect(Collectors.toList());

                                List<String> _documentIds = _segments.stream()
                                        .map(Document::getId)
                                        .distinct()
                                        .collect(Collectors.toList());

                                List<DatasetDocument> _medias = datasetRepository.findDocuments(_documentIds, false);
                                _segments = _medias.stream()
                                        .map(i -> {
                                            Segment _segment = new Segment();
                                            _segment.setId(i.getId());
                                            _segment.setName(i.getDocument().getName());
                                            return _segment;
                                        })
                                        .collect(Collectors.toList());
                                if (!_segments.isEmpty()) {
                                    _message.setSegments(_segments);

                                    try {
                                        sse.send(_message);
                                    } catch (IOException e) {
                                        sse.completeWithError(e);
                                    }
                                }
                            }

                            break;
                        case "error":
                            String _error = JSONPath.read(data, "$.message", String.class);
                            sse.completeWithError(new Exception(_error));

                            break;
                    }

                }

                public void onClosed(@NotNull EventSource eventSource) {
                    sse.complete();
                }

                public void onFailure(@NotNull EventSource eventSource, Throwable e, Response response) {
                    sse.completeWithError(e);
                }
            };

            if (StringUtils.hasLength(rerankingModelName)) {
                consoleFacade.chat(appId, _conversationId, model, _provider, params.prompt, datasetIds, retrievalModel, retrievalTopK, retrievalScore, rerankingModelName, rerankingProviderName, message, _listener);
            } else {
                consoleFacade.chat(appId, _conversationId, model, _provider, params.prompt, datasetIds, retrievalModel, retrievalTopK, retrievalScore, keywordWeight, vectorWeight, message, _listener);
            }

        }, id, plugins, message, operatorId);
    }

    protected SseEmitter generate(BiConsumer<TEMP, SseEmitter> function, String id, List<String> plugins, String message, String operatorId) {
        SseEmitter _sse = new SseEmitter(-1L);

        Result<T> _record = ((ConversationRepository<T>) AopContext.currentProxy()).permit(id, operatorId);
        if (!_record.isOK()) {
            _sse.completeWithError(new IllegalAccessException(_record.message));
            return _sse;
        }

        TEMP _params = new TEMP();
        _params.setDifyId(_record.data.getDifyId());

        StringBuilder _system = new StringBuilder();
        if (plugins != null) {
            for (IPlugin i : pluginImpls) {
                if (i.support(plugins)) {
                    Pair<String, List<?>> _temp = i.invoke(id, _record.data.getDifyId(), message, operatorId);
                    switch (i.getId()) {
                        case ParserPlugin.PLUGIN_ID:
                            if (!_temp.getValue().isEmpty()) {
                                _system.append(_temp.getKey());
                                _temp.getValue().forEach(j -> _params.documents.add(new Document((ParserPlugin.Document) j)));
                            }

                            break;
                        case WebSearchPlugin.PLUGIN_ID:
                            if (!_temp.getValue().isEmpty()) {
                                _system.append(_temp.getKey());
                                _temp.getValue().forEach(j -> _params.links.add((IWebSearchService.Page) j));
                            }

                            break;
                    }
                }
            }

            if (StringUtils.hasLength(_system.toString())) {
                _system.append("请基于以上信息和您的知识回答用户的问题。如果提供的信息对回答有帮助，请引用相关信息，并在回答中标记信息的[序号]。");
            }
        }

        _params.prompt = _system.toString();
        function.accept(_params, _sse);
        return _sse;
    }

    @Transactional(readOnly = true)
    public List<ConversationDocument> findDocuments(String conversationId, boolean brief, String userId) {
        List<ConversationDocument> _documents = stream(ConversationDocument.class).where(i -> conversationId.equals(i.getConversationId()) && userId.equals(i.getCreatorId()))
                .toList();
        if (CollectionUtils.isEmpty(_documents)) {
            return _documents;
        }

        if (!brief) {
            List<String> _mediaIds = _documents.stream()
                    .map(ConversationDocument::getId)
                    .collect(Collectors.toList());

            List<Media> _medias = mediaRepository.find(_mediaIds, null);

            _documents.forEach(i -> {
                Media _media = _medias.stream()
                        .filter(j -> Objects.equals(i.getId(), j.getId()))
                        .findFirst().orElse(null);
                i.setDocument(_media);
            });
        }

        return _documents;
    }

    /**
     * 上传文档
     *
     * @param conversationId
     * @param bucket
     * @param file
     * @param user
     * @return
     */
    public Result<Media> uploadDocument(String conversationId, String bucket, File file, User user) {
        Result<Media> _media = new Result<>();

        Result<T> _record = ((ConversationRepository<T>) AopContext.currentProxy()).permit(conversationId, user.getId());
        if (!_record.isOK()) {
            return _media.pack(_record);
        }

        // 保存文件
        _media = mediaRepository.add(bucket, file, true, user.getId());
        if (!_media.isOK()) {
            return _media;
        }

        // 关联会话
        ConversationDocument _document = new ConversationDocument(_media.data.getId(), conversationId);
        _document.setCreatorId(user.getId());
        _document.setCreateTime(new Date());
        getCurrentSession().save(_document);

        return _media;
    }

    /**
     * 删除文档
     *
     * @param documentId
     * @param operatorId
     * @return
     */
    public Result<Void> removeDocument(String documentId, String operatorId) {
        Result<Void> _success = new Result<>();

        ConversationDocument _document = getCurrentSession().get(ConversationDocument.class, documentId);
        if (_document == null) {
            _success.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{ConversationDocument.class.getSimpleName()});
            return _success;
        }

        return removeDocument(_document, operatorId);
    }

    /**
     * 删除文档
     *
     * @param document
     * @param operatorId
     * @return
     */
    public Result<Void> removeDocument(ConversationDocument document, String operatorId) {
        Result<Void> _success = new Result<>();

        if (!Objects.equals(document.getCreatorId(), operatorId)) {
            _success.setCode(Result.DATA_ACCESS_DENY);
            return _success;
        }

        _success = mediaRepository.remove(document.getId(), true, operatorId);
        if (!_success.isOK()) {
            return _success;
        }

        getCurrentSession().remove(document);

        return _success;
    }

    public Result<Void> removeDocumentsByConversationId(String conversationId, String operatorId) {
        Result<Void> _success = new Result<>();

        for (ConversationDocument i : ((ConversationRepository<T>) AopContext.currentProxy()).findDocuments(conversationId, true, operatorId)) {
            removeDocument(i, operatorId);
        }

        return _success;
    }

    public static class TEMP {

        String difyId;
        String prompt;
        List<Document> documents = new ArrayList();
        List<Segment> segments = new ArrayList();
        List<IWebSearchService.Page> links = new ArrayList();

        public String getDifyId() {
            return difyId;
        }

        public void setDifyId(String difyId) {
            this.difyId = difyId;
        }

        public String getPrompt() {
            return this.prompt;
        }

        public void setPrompt(String prompt) {
            this.prompt = prompt;
        }

        public List<Document> getDocuments() {
            return this.documents;
        }

        public void setDocuments(List<Document> documents) {
            this.documents = documents;
        }

        public List<Segment> getSegments() {
            return this.segments;
        }

        public void setSegments(List<Segment> segments) {
            this.segments = segments;
        }

        public List<IWebSearchService.Page> getLinks() {
            return this.links;
        }

        public void setLinks(List<IWebSearchService.Page> links) {
            this.links = links;
        }

    }

    public static class UpdateConversationIdEvent {

        String id;
        String difyId;
        String userId;

        public UpdateConversationIdEvent(String id, String difyId, String userId) {
            this.id = id;
            this.difyId = difyId;
            this.userId = userId;
        }

        public String getId() {
            return this.id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getDifyId() {
            return this.difyId;
        }

        public void setDifyId(String difyId) {
            this.difyId = difyId;
        }

        public String getUserId() {
            return this.userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

    }

    public static class ResponseMessage {

        String text;
        Memory.Reasoning reasoning;
        List<Document> documents;
        List<Segment> segments;
        List<IWebSearchService.Page> links;

        public String getText() {
            return this.text;
        }

        public void setText(String text) {
            this.text = text;
        }

        public Memory.Reasoning getReasoning() {
            return this.reasoning;
        }

        public void setReasoning(Memory.Reasoning reasoning) {
            this.reasoning = reasoning;
        }

        public List<Document> getDocuments() {
            return this.documents;
        }

        public void setDocuments(List<Document> documents) {
            this.documents = documents;
        }

        public List<Segment> getSegments() {
            return this.segments;
        }

        public void setSegments(List<Segment> segments) {
            this.segments = segments;
        }

        public List<IWebSearchService.Page> getLinks() {
            return this.links;
        }

        public void setLinks(List<IWebSearchService.Page> links) {
            this.links = links;
        }

    }

    public static class Document {

        String id;
        String name;
        String content;

        public Document() {
        }

        public Document(ParserPlugin.Document document) {
            this.id = document.getId();
            this.name = document.getName();
            this.content = document.getContent();
        }

        public String getId() {
            return this.id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return this.name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getContent() {
            return this.content;
        }

        public void setContent(String content) {
            this.content = content;
        }

    }

    public static class Segment extends Document {

        Double score;

        public Double getScore() {
            return this.score;
        }

        public void setScore(Double score) {
            this.score = score;
        }

    }

}