package com.chinamobile.sparrow.ai.repository.llm;

import com.chinamobile.sparrow.ai.model.llm.Dataset;
import com.chinamobile.sparrow.ai.model.llm.DatasetDocument;
import com.chinamobile.sparrow.ai.service.dify.DatasetFacade;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PaginatedRecords;
import com.chinamobile.sparrow.domain.lang.ResultWarpperRuntimeException;
import com.chinamobile.sparrow.domain.model.media.Media;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import com.chinamobile.sparrow.domain.util.DateUtil;
import org.apache.commons.io.FilenameUtils;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Pair;
import org.springframework.aop.framework.AopContext;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class DatasetRepository<T extends Dataset> extends AbstractEntityRepository<T> {

    protected final String applicationName;
    protected final AbstractMediaRepository mediaRepository;
    protected final DatasetFacade datasetFacade;

    public DatasetRepository(EntityManagerFactory entityManagerFactory, JinqJPAStreamProvider jinqJPAStreamProvider, Class<T> tClass, String applicationName, AbstractMediaRepository mediaRepository, DatasetFacade datasetFacade) {
        super(entityManagerFactory, jinqJPAStreamProvider, tClass);
        this.applicationName = applicationName;
        this.mediaRepository = mediaRepository;
        this.datasetFacade = datasetFacade;
    }

    @Transactional(readOnly = true)
    public Result<T> get(String id) {
        Result<T> _record = new Result<>();

        _record.data = getCurrentSession().get(tClass, id);
        if (_record.data == null) {
            _record.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{tClass.getSimpleName()});
        }

        return _record;
    }

    @Transactional(readOnly = true)
    public Result<T> permit(String id, String userId) {
        Result<T> _record = get(id);
        if (!_record.isOK()) {
            return _record;
        }

        if (!Objects.equals(userId, _record.data.getCreatorId())) {
            _record.setCode(Result.DATA_ACCESS_DENY);
        }

        return _record;
    }

    @Transactional(readOnly = true)
    public PaginatedRecords<T> search(int count, int index, String title) {
        JinqStream<T> _query = stream(tClass);

        if (StringUtils.hasLength(title)) {
            _query = _query.where(i -> i.getTitle() != null && i.getTitle().contains(title));
        }

        PaginatedRecords<T> _page = new PaginatedRecords<>(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip((long) count * index).limit(count);
        }
        _page.records = _query.toList();

        return _page;
    }

    public List<T> me(String userId) {
        return stream(tClass).where(i -> userId.equals(i.getCreatorId())).toList();
    }

    public Result<String> save(T record, User operator) throws InstantiationException, IllegalAccessException {
        Result<String> _id = new Result<>();

        boolean _alreadyExisted = true;

        Result<T> _record = ((DatasetRepository<T>) AopContext.currentProxy()).get(record.getId());
        if (!_record.isOK()) {
            _record.data = tClass.newInstance();

            String _name = String.format("%s:%s", this.applicationName, record.getTitle());
            if (_name.length() > 40) {
                _name = _name.substring(0, 37) + "...";
            }

            Result<String> _temp = datasetFacade.create(_name, DatasetFacade.ENUM_INDEXING_TECHNIQUE.high_quality, String.format("%s\n[%s]%s\n%s", _record.data.getId(), operator.getAccount(), operator.getName(), DateUtil.toString(new Date(), "yyyy-MM-dd HH:mm:ss")), DatasetFacade.ENUM_PERMISSION.only_me);
            if (!_temp.isOK()) {
                return _id.pack(_temp);
            }

            _record.data.setDifyId(_temp.data);
            _alreadyExisted = false;
        }

        _record.data.setTitle(record.getTitle());

        Result<Void> _success = _alreadyExisted ? update(_record.data, operator.getId()) : add(_record.data, operator.getId());
        if (!_success.isOK()) {
            // 删除知识库
            if (!_alreadyExisted) {
                datasetFacade.delete(_record.data.getDifyId());
            }

            return _id.pack(_success);
        }

        _id.data = _record.data.getId();
        return _id;
    }

    @Transactional
    public Result<Void> remove(String id, String operatorId) throws IOException {
        Result<Void> _success = new Result<>();

        Result<T> _record = ((DatasetRepository<T>) AopContext.currentProxy()).permit(id, operatorId);
        if (!_record.isOK()) {
            return _success.pack(_record);
        }

        // 删除记录
        getCurrentSession().remove(_record.data);

        // 删除知识库
        _success = datasetFacade.delete(_record.data.getDifyId());
        if (!_success.isOK()) {
            throw new ResultWarpperRuntimeException(_success);
        }

        return _success;
    }

    @Transactional(readOnly = true)
    public PaginatedRecords<DatasetDocument> searchDocuments(int count, int index, String id, String name, boolean brief) {
        JinqStream<DatasetDocument> _query = stream(DatasetDocument.class);

        if (StringUtils.hasLength(id)) {
            _query = _query.where(i -> id.equals(i.getDatasetId()));
        }

        if (StringUtils.hasLength(name)) {
            _query.leftOuterJoin((i, session) -> session.stream(Media.class), (i, j) -> j.getId().equals(i.getId()))
                    .where(i -> i.getTwo() != null)
                    .where(i -> i.getTwo().getName().contains(name))
                    .select(Pair::getOne);
        }

        PaginatedRecords<DatasetDocument> _page = new PaginatedRecords<>(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip((long) count * index).limit(count);
        }
        _page.records = _query.toList();

        if (!brief) {
            parseDocuments(_page.records);
        }

        return _page;
    }

    @Transactional(readOnly = true)
    public List<DatasetDocument> findDocuments(List<String> documentIds, boolean brief) {
        List<DatasetDocument> _documents = stream(DatasetDocument.class).where(i -> documentIds.contains(i.getDocumentId()))
                .toList();

        if (!brief) {
            parseDocuments(_documents);
        }

        return _documents;
    }

    public Result<Media> uploadDocument(String id, String bucket, File file, String description, String operatorId) throws IOException {
        Result<Media> _media = new Result<>();

        Result<T> _record = ((DatasetRepository<T>) AopContext.currentProxy()).get(id);
        if (!_record.isOK()) {
            return _media.pack(_record);
        }

        _media = mediaRepository.add(bucket, file, true, operatorId);
        if (!_media.isOK()) {
            return _media;
        }

        Result<String> _documentId;
        if (StringUtils.hasLength(description)) {
            String _name = FilenameUtils.getBaseName(_media.data.getName()) + "-" + System.currentTimeMillis() + FilenameUtils.getExtension(file.getName());
            _documentId = datasetFacade.uploadText(_record.data.getDifyId(), _name, description, DatasetFacade.ENUM_INDEXING_TECHNIQUE.high_quality);
        } else {
            _documentId = datasetFacade.uploadFile(_record.data.getDifyId(), file, DatasetFacade.ENUM_INDEXING_TECHNIQUE.high_quality);
        }
        if (!_documentId.isOK()) {
            throw new ResultWarpperRuntimeException(_documentId);
        }

        // 关联知识库
        DatasetDocument _document = new DatasetDocument(_media.data.getId(), id, _documentId.data, description);
        _document.setCreatorId(operatorId);
        _document.setCreateTime(new Date());
        getCurrentSession().save(_document);

        return _media;
    }

    public Result<Void> updateDocument(String id, String description, String operatorId) {
        Result<Void> _success = new Result<>();

        DatasetDocument _document = stream(DatasetDocument.class).where(i -> id.equals(i.getId()))
                .findFirst().orElse(null);
        if (_document == null) {
            _success.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{DatasetDocument.class.getSimpleName()});
        } else {
            _document.setDescription(description);
            _document.setMaintainerId(operatorId);
            _document.setMaintainTime(new Date());

            getCurrentSession().update(_document);
        }

        return _success;
    }

    /**
     * 删除文档
     *
     * @param documentId
     * @param operatorId
     * @return
     */
    public Result<Void> removeDocument(String documentId, String operatorId) {
        Result<Void> _success = new Result<>();

        Pair<DatasetDocument, Dataset> _relation = stream(DatasetDocument.class)
                .where(i -> documentId.equals(i.getId()))
                .leftOuterJoin((i, source) -> source.stream(Dataset.class), (i, j) -> j.getId().equals(i.getDatasetId()))
                .findFirst().orElse(null);
        if (_relation == null) {
            _success.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{DatasetDocument.class.getSimpleName()});
            return _success;
        }

        // 删除知识库文档
        if (_relation.getTwo() != null) {
            _success = datasetFacade.deleteDocument(_relation.getTwo().getId(), documentId);
            if (!_success.isOK()) {
                return _success;
            }
        }

        // 删除文件
        _success = mediaRepository.remove(documentId, true, operatorId);
        if (!_success.isOK()) {
            return _success;
        }

        // 删除关联
        getCurrentSession().remove(_relation.getOne());

        return _success;
    }

    void parseDocuments(List<DatasetDocument> documents) {
        if (CollectionUtils.isEmpty(documents)) {
            return;
        }

        List<String> _ids = documents.stream()
                .map(DatasetDocument::getId)
                .collect(Collectors.toList());
        List<Media> _documents = mediaRepository.find(_ids, null);
        for (DatasetDocument i : documents) {
            Media _document = _documents.stream()
                    .filter(j -> Objects.equals(i.getId(), j.getId()))
                    .findFirst().orElse(null);
            i.setDocument(_document);
        }
    }

}