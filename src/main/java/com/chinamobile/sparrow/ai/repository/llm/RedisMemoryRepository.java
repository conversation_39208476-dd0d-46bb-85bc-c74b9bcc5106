package com.chinamobile.sparrow.ai.repository.llm;

import com.agentsflex.core.message.Message;
import com.chinamobile.sparrow.ai.infra.llm.memory.ChatMemory;
import com.chinamobile.sparrow.ai.infra.llm.memory.RedisChatMemory;
import com.chinamobile.sparrow.ai.model.llm.Conversation;
import com.chinamobile.sparrow.ai.model.llm.Memory;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.springframework.aop.framework.AopContext;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.JdkSerializationRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class RedisMemoryRepository<T extends Memory> extends AbstractMemoryRepository<T> {

    protected final String redisKeyPrefix;
    protected final RedisTemplate<String, Object> redisTemplate;

    public RedisMemoryRepository(EntityManagerFactory entityManagerFactory, JinqJPAStreamProvider jinqJPAStreamProvider, Class<T> tClass, Integer memorySize, String redisKeyPrefix, ConversationRepository<? extends Conversation> conversationRepository, AbstractMediaRepository mediaRepository, RedisConnectionFactory redisConnectionFactory) {
        super(entityManagerFactory, jinqJPAStreamProvider, tClass, memorySize, conversationRepository, mediaRepository);
        this.redisKeyPrefix = redisKeyPrefix;
        this.redisTemplate = new RedisTemplate<>();
        this.redisTemplate.setConnectionFactory(redisConnectionFactory);
        this.redisTemplate.setKeySerializer(new StringRedisSerializer());
        this.redisTemplate.setValueSerializer(new JdkSerializationRedisSerializer());
        this.redisTemplate.afterPropertiesSet();
    }

    @Override
    public ChatMemory getChatMemory(String conversationId) {
        return StringUtils.hasLength(conversationId)
                ? new RedisChatMemory(redisKeyPrefix, memorySize, (RedisMemoryRepository<T>) AopContext.currentProxy(), mediaRepository, redisTemplate)
                : new RedisChatMemory(conversationId, redisKeyPrefix, memorySize, (RedisMemoryRepository<T>) AopContext.currentProxy(), mediaRepository, redisTemplate);
    }

    @Override
    public Map<String, List<Message>> getStorages() {
        Set<String> _keys = redisTemplate.keys(this.redisKeyPrefix + "*");

        Map<String, List<Message>> _storages = new HashMap<>();
        _keys.forEach(i -> {
            List<Object> _cache = redisTemplate.opsForList().range(i, 0L, -1L);
            if (_cache != null) {
                _storages.put(i.replace(redisKeyPrefix, ""), _cache.stream()
                        .map(j -> (Message) j)
                        .collect(Collectors.toList()));
            }
        });

        return _storages;
    }

}