package com.chinamobile.sparrow.ai.repository.llm;

import com.agentsflex.core.message.Message;
import com.chinamobile.sparrow.ai.model.llm.Memory;
import com.chinamobile.sparrow.domain.infra.job.JobDetailAndTrigger;
import org.jetbrains.annotations.NotNull;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobExecutionContext;
import org.quartz.PersistJobDataAfterExecution;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.quartz.QuartzJobBean;

import java.util.List;
import java.util.Map;

@DisallowConcurrentExecution
@PersistJobDataAfterExecution
@JobDetailAndTrigger(jobName = "memory-persist", jobGroup = "llm", triggerName = "memory-persist", triggerGroup = "llm", triggerCron = "0/1 * * * * ?", triggerOnStart = false)
public class MemoryPersistJob extends QuartzJobBean {

    final AbstractMemoryRepository<? extends Memory> memoryRepository;
    final Logger logger;

    public MemoryPersistJob(AbstractMemoryRepository<? extends Memory> memoryRepository) {
        this.memoryRepository = memoryRepository;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    protected void executeInternal(@NotNull JobExecutionContext jobExecutionContext) {
        logger.info("作业开始");

        try {
            int _count = 0;

            Map<String, List<Message>> _memories = memoryRepository.getStorages();
            for (Map.Entry<String, List<Message>> i : _memories.entrySet()) {
                _count += memoryRepository.save(i.getKey(), i.getValue(), null);
            }
            logger.info("已保存{}个对话的{}条消息", _memories.size(), _count);
        } catch (Throwable e) {
            logger.error("作业执行时程序异常", e);
        } finally {
            logger.info("作业结束");
        }
    }

}