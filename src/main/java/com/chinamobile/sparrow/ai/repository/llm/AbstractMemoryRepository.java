package com.chinamobile.sparrow.ai.repository.llm;

import com.agentsflex.core.message.AiMessage;
import com.agentsflex.core.message.HumanMessage;
import com.agentsflex.core.message.Message;
import com.agentsflex.core.prompt.HistoriesPrompt;
import com.agentsflex.core.prompt.ImagePrompt;
import com.chinamobile.sparrow.ai.infra.llm.memory.ChatMemory;
import com.chinamobile.sparrow.ai.model.llm.Conversation;
import com.chinamobile.sparrow.ai.model.llm.Memory;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.repository.AbstractMediaAvailableRepository;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.IdWorker;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

public abstract class AbstractMemoryRepository<T extends Memory> extends AbstractMediaAvailableRepository<T> {

    protected final Integer memorySize;
    protected final ConversationRepository<? extends Conversation> conversationRepository;

    protected final List<ChatMemory> memories;
    protected final Map<String, HistoriesPrompt> prompts;

    public AbstractMemoryRepository(EntityManagerFactory entityManagerFactory, JinqJPAStreamProvider jinqJPAStreamProvider, Class<T> tClass, Integer memorySize, ConversationRepository<? extends Conversation> conversationRepository, AbstractMediaRepository mediaRepository) {
        super(entityManagerFactory, jinqJPAStreamProvider, mediaRepository, tClass);
        this.memorySize = memorySize;
        this.conversationRepository = conversationRepository;
        this.memories = new CopyOnWriteArrayList<>();
        this.prompts = new ConcurrentHashMap<>();
    }

    @Override
    public List<String> getMediaIds(List<T> records) {
        return CollectionUtils.isEmpty(records) ? new ArrayList<>() : records.stream()
                .map(Memory::getMediaId)
                .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public List<T> find(int count, String conversationId) {
        JinqStream<T> _query = stream(tClass).where(i -> conversationId.equals(i.getConversationId()))
                .sortedBy(Memory::getTimestamp);

        if (count > 0) {
            _query = _query.limit(count);
        }
        List<T> _records = _query.toList();

        parseReasoning(_records);
        return _records;
    }

    /**
     * 保存
     *
     * @param conversationId
     * @param messages
     * @param operatorId
     */
    public int save(String conversationId, List<Message> messages, String operatorId) {
        List<String> _ids = stream(tClass).where(i -> conversationId.equals(i.getConversationId()))
                .select(Memory::getId)
                .toList();

        List<T> _records = (
                _ids.isEmpty()
                        ? messages.stream()
                        : messages.stream()
                        .filter(i -> !_ids.contains(i.getMetadata(ConversationRepository.MESSAGE_METADATA_ID).toString()))
        )
                .map(i -> {
                    try {
                        T _record = tClass.newInstance();
                        _record.setId(i.getMetadata(ConversationRepository.MESSAGE_METADATA_ID).toString());
                        _record.setConversationId(conversationId);
                        _record.setTimestamp((Date) i.getMetadata(ConversationRepository.MESSAGE_METADATA_TIMESTAMP));

                        if (i instanceof ImagePrompt.TextAndImageMessage) {
                            _record.setMediaId(i.getMetadata(ConversationRepository.MESSAGE_METADATA_MEDIA_ID).toString());
                            _record.setMessage(((ImagePrompt.TextAndImageMessage) i).getPrompt().getContent());
                        } else {
                            _record.setMessage(i.getMessageContent().toString());
                        }

                        if (i instanceof HumanMessage) {
                            _record.setUserId(i.getMetadata(ConversationRepository.MESSAGE_METADATA_USER).toString());
                        } else if (i instanceof AiMessage) {
                            if (i.getMetadataMap().containsKey(ConversationRepository.MESSAGE_METADATA_REASONING)) {
                                Memory.Reasoning _reasoning = new Memory.Reasoning();
                                _reasoning.setDescription(i.getMetadata(ConversationRepository.MESSAGE_METADATA_REASONING).toString());
                                _reasoning.setDuration((Long) i.getMetadata(ConversationRepository.MESSAGE_METADATA_REASONING_DURATION));
                                _record.setReasoningKey(ConverterUtil.toJson(_reasoning));
                            }
                        }

                        return _record;
                    } catch (IllegalAccessException | InstantiationException e) {
                        throw new RuntimeException(e);
                    }
                })
                .collect(Collectors.toList());

        int _count = 0;

        for (T i : _records) {
            add(i, operatorId);
            ++_count;
        }

        return _count;
    }

    /**
     * 清理
     *
     * @param conversationId
     * @return
     */
    public Result<Void> remove(String conversationId, String operatorId) {
        Result<Void> _success = new Result<>();

        if (!conversationRepository.accessible(conversationId, operatorId)) {
            _success.setCode(Result.DATA_ACCESS_DENY);
            return _success;
        }

        clear(conversationId);

        _success = conversationRepository.removeDocumentsByConversationId(conversationId, operatorId);
        if (!_success.isOK()) {
            return _success;
        }

        for (String i : stream(Memory.class).where(i -> conversationId.equals(i.getConversationId()) && i.getMediaId() != null)
                .select(Memory::getMediaId)
                .toList()) {
            mediaRepository.remove(i, false, operatorId);
        }

        getCurrentSession().createQuery("delete from Memory where conversationId = :conversationId")
                .setParameter("conversationId", conversationId)
                .executeUpdate();

        return _success;
    }

    public void clear(String conversationId) {
        HistoriesPrompt _prompt = prompt(conversationId);
        ((ChatMemory) _prompt.getMemory()).clear();
    }

    public String id() {
        return String.valueOf(IdWorker.getInstance().nextId());
    }

    public HistoriesPrompt prompt(String conversationId) {
        if (!StringUtils.hasLength(conversationId)) {
            ChatMemory _memory = getChatMemory(null);
            memories.add(_memory);

            HistoriesPrompt _prompt = new HistoriesPrompt(_memory);
            prompts.put(_memory.id().toString(), _prompt);
            return _prompt;
        }

        HistoriesPrompt _prompt = prompts.get(conversationId);
        if (_prompt != null) {
            return _prompt;
        }

        ChatMemory _memory = memories.stream()
                .filter(i -> conversationId.equals(i.id()))
                .findFirst().orElse(null);
        if (_memory == null) {
            _memory = getChatMemory(conversationId);
            memories.add(_memory);
        }

        _prompt = new HistoriesPrompt(_memory);
        _prompt.setHistoryMessageTruncateEnable(true);

        prompts.put(_memory.id().toString(), _prompt);

        return _prompt;
    }

    public abstract ChatMemory getChatMemory(String conversationId);

    public abstract Map<String, List<Message>> getStorages();

    public List<Message> parseMessages(List<T> records) {
        return records.stream()
                .map(i -> {
                    Message _message;

                    if (StringUtils.hasLength(i.getUserId())) {
                        if (StringUtils.hasLength(i.getMediaId())) {
                            ImagePrompt _temp = new ImagePrompt(i.getMessage());
                            _temp.setImageBase64(i.getMediaId());

                            _message = new ImagePrompt.TextAndImageMessage(_temp);
                        } else {
                            _message = new HumanMessage(i.getMessage());
                        }

                        _message.addMetadata(ConversationRepository.MESSAGE_METADATA_USER, i.getUserId());
                    } else {
                        _message = new AiMessage();
                        ((AiMessage) _message).setContent(i.getMessage());

                        if (i.getReasoning() != null) {
                            _message.addMetadata(ConversationRepository.MESSAGE_METADATA_REASONING, i.getReasoning().getDescription());
                            _message.addMetadata(ConversationRepository.MESSAGE_METADATA_REASONING_DURATION, i.getReasoning().getDuration());
                        }
                    }

                    _message.addMetadata(ConversationRepository.MESSAGE_METADATA_ID, i.getId());
                    _message.addMetadata(ConversationRepository.MESSAGE_METADATA_TIMESTAMP, i.getCreateTime());
                    return _message;
                })
                .collect(Collectors.toList());
    }

    void parseReasoning(List<T> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        records.stream()
                .filter(i -> StringUtils.hasLength(i.getReasoningKey()))
                .forEach(i -> i.setReasoning(ConverterUtil.json2Object(i.getReasoningKey(), Memory.Reasoning.class)));
    }

}