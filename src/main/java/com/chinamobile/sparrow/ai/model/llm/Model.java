package com.chinamobile.sparrow.ai.model.llm;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;
import org.springframework.util.StringUtils;

import javax.persistence.*;

@Entity
@Table(name = "llm_models")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
public class Model extends AbstractEntity {

    @Id
    String id = String.valueOf(IdWorker.getInstance().nextId());

    @Column(unique = true, nullable = false)
    String name;

    @Column(nullable = false)
    String title;

    @Column(nullable = false)
    String baseURL;

    @Column(nullable = false)
    String apiKey;

    @Column(nullable = false)
    ENUM_API apiType;

    @Column(columnDefinition = "text")
    String provider;

    @Column(nullable = false)
    ENUM_MODE mode;

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = StringUtils.trimWhitespace(name);
    }

    public String getTitle() {
        return this.title;
    }

    public void setTitle(String title) {
        this.title = StringUtils.trimWhitespace(title);
    }

    public String getBaseURL() {
        return this.baseURL;
    }

    public void setBaseURL(String baseURL) {
        this.baseURL = StringUtils.trimWhitespace(baseURL);
    }

    public String getApiKey() {
        return this.apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = StringUtils.trimWhitespace(apiKey);
    }

    public ENUM_API getApiType() {
        return this.apiType;
    }

    public void setApiType(ENUM_API apiType) {
        this.apiType = apiType;
    }

    public String getProvider() {
        return this.provider;
    }

    public void setProvider(String provider) {
        this.provider = StringUtils.trimWhitespace(provider);
    }

    public ENUM_MODE getMode() {
        return this.mode;
    }

    public void setMode(ENUM_MODE mode) {
        this.mode = mode;
    }

    public static enum ENUM_API {
        OPEN_AI(0), AZURE_OPEN_AI(1), QWEN(2), DEEP_SEEK(3);

        final int value;

        ENUM_API(int value) {
            this.value = value;
        }

        public static ENUM_API valueOf(int value) {
            switch (value) {
                case 0:
                    return OPEN_AI;
                case 1:
                    return AZURE_OPEN_AI;
                case 2:
                    return QWEN;
                case 3:
                    return DEEP_SEEK;
                default:
                    return null;
            }
        }

        public int value() {
            return this.value;
        }
    }

    public static enum ENUM_MODE {
        EMBEDDING(0), DEFAULT(1), REASONING(2), MULTIMODAL(3);

        final int value;

        ENUM_MODE(int value) {
            this.value = value;
        }

        public static ENUM_MODE valueOf(int value) {
            switch (value) {
                case 0:
                    return EMBEDDING;
                case 1:
                    return DEFAULT;
                case 2:
                    return REASONING;
                case 3:
                    return MULTIMODAL;
                default:
                    return null;
            }
        }

        public int value() {
            return this.value;
        }
    }

}