package com.chinamobile.sparrow.ai.model.llm;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;
import org.springframework.util.StringUtils;

import javax.persistence.*;

@Entity
@Table(name = "llm_conversation_records")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
public class Conversation extends AbstractEntity {

    @Id
    @Column(length = 36)
    String id = String.valueOf(IdWorker.getInstance().nextId());

    String title;

    @Column(length = 36)
    String difyId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = StringUtils.trimWhitespace(title);
    }

    public String getDifyId() {
        return difyId;
    }

    public void setDifyId(String datasetId) {
        this.difyId = datasetId;
    }

}