package com.chinamobile.sparrow.ai.model.llm;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;
import org.springframework.util.StringUtils;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "llm_conversation_memories")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
public class Memory extends AbstractEntity {

    @Id
    @Column(length = 36)
    protected String id = String.valueOf(IdWorker.getInstance().nextId());

    @Column(length = 36)
    protected String conversationId;

    @Column(columnDefinition = "text")
    protected String reasoningKey;

    @Transient
    protected Reasoning reasoning;

    @Column(columnDefinition = "text")
    protected String message;

    @Column(length = 36)
    protected String mediaId;

    @Column(length = 36)
    protected String userId;

    protected Date timestamp;

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getConversationId() {
        return this.conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public String getReasoningKey() {
        return this.reasoningKey;
    }

    public void setReasoningKey(String reasoningKey) {
        this.reasoningKey = StringUtils.trimWhitespace(reasoningKey);
    }

    public Reasoning getReasoning() {
        return this.reasoning;
    }

    public void setReasoning(Reasoning reasoning) {
        this.reasoning = reasoning;
    }

    public String getMessage() {
        return this.message;
    }

    public void setMessage(String message) {
        this.message = StringUtils.trimWhitespace(message);
    }

    public String getMediaId() {
        return this.mediaId;
    }

    public void setMediaId(String mediaId) {
        this.mediaId = mediaId;
    }

    public String getUserId() {
        return this.userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Date getTimestamp() {
        return this.timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public static class Reasoning {

        String description;
        Long duration;

        public String getDescription() {
            return this.description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public Long getDuration() {
            return this.duration;
        }

        public void setDuration(Long duration) {
            this.duration = duration;
        }

    }

}