package com.chinamobile.sparrow.ai.model.llm;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.model.media.Media;
import org.springframework.util.StringUtils;

import javax.persistence.*;

@Entity
@Table(name = "llm_dataset_documents")
public class DatasetDocument extends AbstractEntity {

    @Id
    @Column(length = 36)
    String id;

    @Column(length = 36)
    String datasetId;

    /**
     * Dify文档Id
     */
    @Column(length = 36)
    String documentId;

    @Column(columnDefinition = "text")
    String description;

    @Transient
    Media document;

    public DatasetDocument() {
    }

    public DatasetDocument(String id, String datasetId, String documentId, String description) {
        this.id = id;
        this.datasetId = datasetId;
        this.documentId = documentId;
        this.description = description;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDatasetId() {
        return datasetId;
    }

    public void setDatasetId(String conversationId) {
        this.datasetId = conversationId;
    }

    public String getDocumentId() {
        return documentId;
    }

    public void setDocumentId(String documentId) {
        this.documentId = documentId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = StringUtils.trimWhitespace(description);
    }

    public Media getDocument() {
        return document;
    }

    public void setDocument(Media document) {
        this.document = document;
    }

}