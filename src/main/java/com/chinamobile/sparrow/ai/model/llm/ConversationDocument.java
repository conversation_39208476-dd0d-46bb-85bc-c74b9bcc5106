package com.chinamobile.sparrow.ai.model.llm;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.model.media.Media;

import javax.persistence.*;

@Entity
@Table(name = "llm_conversation_documents")
public class ConversationDocument extends AbstractEntity {

    @Id
    @Column(length = 36)
    String id;

    @Column(length = 36)
    String conversationId;

    @Transient
    Media document;

    public ConversationDocument() {
    }

    public ConversationDocument(String id, String conversationId) {
        this.id = id;
        this.conversationId = conversationId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getConversationId() {
        return conversationId;
    }

    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    public Media getDocument() {
        return document;
    }

    public void setDocument(Media document) {
        this.document = document;
    }

}