package com.chinamobile.sparrow.ai.model.llm;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;
import org.springframework.util.StringUtils;

import javax.persistence.*;

@Entity
@Table(name = "llm_applications")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
public class Application extends AbstractEntity {

    @Id
    @Column(length = 36)
    String id = String.valueOf(IdWorker.getInstance().nextId());

    @Column(length = 36)
    String platformId;

    @Column(columnDefinition = "text")
    String icon;

    String name;

    @Column(columnDefinition = "text")
    String description;

    ENUM_PLATFORM platform;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPlatformId() {
        return platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = StringUtils.trimWhitespace(platformId);
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = StringUtils.trimWhitespace(icon);
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = StringUtils.trimWhitespace(name);
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = StringUtils.trimWhitespace(description);
    }

    public ENUM_PLATFORM getPlatform() {
        return platform;
    }

    public void setPlatform(ENUM_PLATFORM platform) {
        this.platform = platform;
    }

    public static enum ENUM_PLATFORM {
        LOCAL(0), DIFY(1);

        final int value;

        ENUM_PLATFORM(int value) {
            this.value = value;
        }

        public static ENUM_PLATFORM valueOf(int value) {
            switch (value) {
                case 0:
                    return LOCAL;
                case 1:
                    return DIFY;
                default:
                    return null;
            }
        }

        public int value() {
            return this.value;
        }
    }

}