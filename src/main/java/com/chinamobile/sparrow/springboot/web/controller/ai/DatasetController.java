package com.chinamobile.sparrow.springboot.web.controller.ai;

import com.chinamobile.sparrow.ai.infra.AddMediaResult;
import com.chinamobile.sparrow.ai.model.llm.Dataset;
import com.chinamobile.sparrow.ai.model.llm.DatasetDocument;
import com.chinamobile.sparrow.ai.repository.llm.DatasetRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PaginatedRecords;
import com.chinamobile.sparrow.domain.lang.Sorter;
import com.chinamobile.sparrow.domain.model.media.Media;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonObject;
import org.apache.commons.io.FileUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping(value = "llm/dataset")
public class DatasetController {

    protected final String accept;
    protected final DatasetRepository datasetRepository;
    protected final LoginUtil loginUtil;
    protected final Logger logger;

    public DatasetController(String accept, DatasetRepository datasetRepository, LoginUtil loginUtil) {
        this.accept = accept;
        this.datasetRepository = datasetRepository;
        this.loginUtil = loginUtil;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @PostMapping(value = "/accept")
    public Result<String> accept() {
        Result<String> _accept = new Result<>();
        _accept.data = accept;
        return _accept;
    }

    @PostMapping(value = "/get")
    @RequiresPermissions(value = "llm:dataset:get")
    public Result<Dataset> get(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        Result<Dataset> _record = datasetRepository.get(_id);
        if (_record.isOK()) {
            datasetRepository.parseUsers(Collections.singletonList(_record.data));
        }

        return _record;
    }

    @PostMapping(value = "/search")
    @RequiresPermissions(value = "llm:dataset:search")
    public Result<PaginatedRecords<Dataset>> search(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        List<Sorter> _sorters = (List<Sorter>) Optional.ofNullable(data.get("sortBy"))
                .map(i -> i.isJsonNull() ? null : ConverterUtil.json2Object(i.toString(), new TypeToken<List<Sorter>>() {
                }.getType())).orElse(null);
        String _title = Optional.ofNullable(data.get("title"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        Result<PaginatedRecords<Dataset>> _page = new Result<>();
        _page.data = datasetRepository.search(_count, _index, _title);
        datasetRepository.parseUsers(_page.data.records);
        return _page;
    }

    @PostMapping(value = "/save")
    @RequiresPermissions(value = "llm:dataset:save")
    public Result<String> save(@RequestBody Dataset record) throws InstantiationException, IllegalAccessException {
        return datasetRepository.save(record, loginUtil.getUser());
    }

    @PostMapping(value = "/remove")
    @RequiresPermissions(value = "llm:dataset:remove")
    public Result<Void> remove(@RequestBody JsonObject data) throws IOException {
        String _id = data.get("id").getAsString();

        return datasetRepository.remove(_id, loginUtil.getUserId());
    }

    @PostMapping(value = "/document/search")
    @RequiresPermissions(value = "llm:dataset:document:search")
    public Result<PaginatedRecords<DatasetDocument>> searchDocuments(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        String _id = Optional.ofNullable(data.get("id"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _name = Optional.ofNullable(data.get("name"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        Result<PaginatedRecords<DatasetDocument>> _documents = new Result<>();
        _documents.data = datasetRepository.searchDocuments(_count, _index, _id, _name, false);
        return _documents;
    }

    @PostMapping(value = "/document/upload")
    @RequiresPermissions(value = "llm:dataset:document:upload")
    public Result<List<AddMediaResult>> uploadDocument(@RequestParam(value = "datasetId") String datasetId, @RequestParam(value = "bucket", required = false) String bucket, @RequestParam(value = "description", required = false) String description, @RequestParam(value = "files") MultipartFile[] files) {
        Result<List<AddMediaResult>> _results = new Result<>();
        _results.data = new ArrayList<>();

        for (MultipartFile i : files) {
            File _file = null;

            try {
                _file = new File(FileUtils.getTempDirectory() + File.separator + i.getOriginalFilename());
                FileUtils.copyInputStreamToFile(i.getInputStream(), _file);

                Result<Media> _media = datasetRepository.uploadDocument(datasetId, bucket, _file, description, loginUtil.getUserId());

                AddMediaResult _result = new AddMediaResult();
                _result.setSuccess(_media.isOK());
                if (_result.getSuccess()) {
                    _result.setId(_media.data.getId());
                    _result.setName(_media.data.getName());
                    _result.setContent(_media.data.getContent());
                } else {
                    _result.setMessage(_media.message);
                }

                _results.data.add(_result);
            } catch (Throwable e) {
                logger.error(e.getMessage(), e);

                AddMediaResult _result = new AddMediaResult();
                _result.setSuccess(false);
                _result.setMessage(e.getMessage());

                _results.data.add(_result);
            } finally {
                if (_file != null) {
                    _file.delete();
                }
            }
        }

        return _results;
    }

    @PostMapping(value = "/document/update")
    @RequiresPermissions(value = "llm:dataset:document:update")
    public Result<Void> updateDocument(@RequestBody JsonObject data) {
        String _documentId = data.get("documentId").getAsString();
        String _description = data.get("description").getAsString();

        return datasetRepository.updateDocument(_documentId, _description, loginUtil.getUserId());
    }

    @PostMapping(value = "/document/remove")
    @RequiresPermissions(value = "llm:dataset:document:remove")
    public Result<Void> removeDocument(@RequestBody JsonObject data) {
        String _documentId = data.get("documentId").getAsString();

        return datasetRepository.removeDocument(_documentId, loginUtil.getUserId());
    }

}