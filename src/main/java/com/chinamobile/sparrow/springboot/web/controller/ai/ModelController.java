package com.chinamobile.sparrow.springboot.web.controller.ai;

import com.chinamobile.sparrow.ai.model.llm.Model;
import com.chinamobile.sparrow.ai.repository.llm.ModelRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PaginatedRecords;
import com.chinamobile.sparrow.domain.lang.Sorter;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonObject;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping(value = "llm/model")
public class ModelController {

    protected final ModelRepository modelRepository;
    protected final LoginUtil loginUtil;

    public ModelController(ModelRepository modelRepository, LoginUtil loginUtil
    ) {
        this.modelRepository = modelRepository;
        this.loginUtil = loginUtil;
    }

    @PostMapping(value = "/get")
    @RequiresPermissions(value = "llm:model:get")
    public Result<Model> get(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        Result<Model> _record = modelRepository.get(_id);
        if (_record.isOK()) {
            modelRepository.parseUsers(Collections.singletonList(_record.data));
        }

        return _record;
    }

    @PostMapping(value = "/find")
    public Result<List<Model>> find() {
        Result<List<Model>> _records = new Result<>();
        _records.data = modelRepository.search(null, null, null);
        return _records;
    }

    @PostMapping(value = "/search")
    @RequiresPermissions(value = "llm:model:search")
    public Result<PaginatedRecords<Model>> search(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        List<Sorter> _sorters = (List<Sorter>) Optional.ofNullable(data.get("sortBy"))
                .map(i -> i.isJsonNull() ? null : ConverterUtil.json2Object(i.toString(), new TypeToken<List<Sorter>>() {
                }.getType())).orElse(null);
        String _title = Optional.ofNullable(data.get("title"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        Model.ENUM_MODE _mode = Optional.ofNullable(data.get("mode"))
                .map(i -> i.isJsonNull() ? null : Model.ENUM_MODE.valueOf(i.getAsString())).orElse(null);

        Result<PaginatedRecords<Model>> _page = new Result<>();
        _page.data = modelRepository.search(_count, _index, _sorters, _title, _mode);
        modelRepository.parseUsers(_page.data.records);
        return _page;
    }

    @PostMapping(value = "/save")
    @RequiresPermissions(value = "llm:model:save")
    public Result<String> save(@RequestBody Model record) throws InstantiationException, IllegalAccessException {
        return modelRepository.save(record, loginUtil.getUserId());
    }

    @PostMapping(value = "/remove")
    @RequiresPermissions(value = "llm:model:remove")
    public Result<Void> remove(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return modelRepository.remove(_id, loginUtil.getUserId());
    }

}