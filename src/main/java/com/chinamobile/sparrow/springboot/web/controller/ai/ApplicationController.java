package com.chinamobile.sparrow.springboot.web.controller.ai;

import com.chinamobile.sparrow.ai.model.llm.Application;
import com.chinamobile.sparrow.ai.repository.llm.ApplicationRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PaginatedRecords;
import com.google.gson.JsonObject;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping(value = "llm/application")
public class ApplicationController {

    protected final ApplicationRepository applicationRepository;
    protected final LoginUtil loginUtil;
    protected final Logger logger;

    public ApplicationController(ApplicationRepository applicationRepository, LoginUtil loginUtil) {
        this.applicationRepository = applicationRepository;
        this.loginUtil = loginUtil;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @PostMapping(value = "/get")
    public Result<Application> get(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        Result<Application> _record = applicationRepository.get(_id);
        if (_record.isOK()) {
            applicationRepository.parseUsers(Collections.singletonList(_record.data));
        }

        return _record;
    }

    @PostMapping(value = "/get/install-id")
    public Result<String> getInstallId(@RequestBody JsonObject data) {
        String _difyId = data.get("difyId").getAsString();

        return applicationRepository.getInstallId(_difyId);
    }

    @PostMapping(value = "/search")
    @RequiresPermissions(value = "llm:application:search")
    public Result<PaginatedRecords<Application>> search(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        String _name = Optional.ofNullable(data.get("name"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        Result<PaginatedRecords<Application>> _page = new Result<>();
        _page.data = applicationRepository.search(_count, _index, _name);
        applicationRepository.parseUsers(_page.data.records);
        return _page;
    }

    @PostMapping(value = "/me")
    public Result<List<Application>> me() {
        Result<List<Application>> _records = new Result<>();
        _records.data = applicationRepository.me(loginUtil.getUserId());
        applicationRepository.parseUsers(_records.data);
        return _records;
    }

    @PostMapping(value = "/save")
    public Result<String> save(@RequestBody Application record) throws InstantiationException, IllegalAccessException {
        return applicationRepository.save(record, loginUtil.getUserId());
    }

    @PostMapping(value = "/remove")
    public Result<Void> remove(@RequestBody JsonObject data) throws IOException {
        String _id = data.get("id").getAsString();

        return applicationRepository.remove(_id, loginUtil.getUserId());
    }

}