package com.chinamobile.sparrow.springboot.web.controller.ai;

import com.chinamobile.sparrow.ai.infra.AddMediaResult;
import com.chinamobile.sparrow.ai.model.llm.Conversation;
import com.chinamobile.sparrow.ai.model.llm.ConversationDocument;
import com.chinamobile.sparrow.ai.repository.llm.ConversationRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.model.media.Media;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import com.google.gson.JsonObject;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping(value = "llm/conversation/document")
public class DocumentController {

    protected final String accept;
    protected final ConversationRepository<? extends Conversation> conversationRepository;
    protected final AbstractMediaRepository mediaRepository;
    protected final LoginUtil loginUtil;
    protected final Logger logger;

    public DocumentController(String accept, ConversationRepository<? extends Conversation> conversationRepository, AbstractMediaRepository mediaRepository, LoginUtil loginUtil) {
        this.accept = accept;
        this.conversationRepository = conversationRepository;
        this.mediaRepository = mediaRepository;
        this.loginUtil = loginUtil;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @PostMapping(value = "/accept")
    public Result<String> accept() {
        Result<String> _accept = new Result<>();
        _accept.data = accept;
        return _accept;
    }

    @PostMapping(value = "/find")
    public Result<List<ConversationDocument>> findDocuments(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        Result<List<ConversationDocument>> _documents = new Result<>();
        _documents.data = conversationRepository.findDocuments(_id, false, loginUtil.getUserId());
        return _documents;
    }

    @PostMapping(value = "/upload")
    public Result<List<AddMediaResult>> uploadDocument(@RequestParam(value = "conversationId") String conversationId, @RequestParam(value = "bucket", required = false) String bucket, @RequestParam(value = "files") MultipartFile[] files) {
        Result<List<AddMediaResult>> _results = new Result<>();
        _results.data = new ArrayList<>();

        for (MultipartFile i : files) {
            File _file = null;

            try {
                _file = new File(FileUtils.getTempDirectory() + File.separator + i.getOriginalFilename());
                FileUtils.copyInputStreamToFile(i.getInputStream(), _file);

                Result<Media> _media = conversationRepository.uploadDocument(conversationId, bucket, _file, loginUtil.getUser());

                AddMediaResult _result = new AddMediaResult();
                _result.setSuccess(_media.isOK());
                if (_result.getSuccess()) {
                    _result.setId(_media.data.getId());
                    _result.setName(_media.data.getName());
                    _result.setContent(_media.data.getContent());
                } else {
                    _result.setMessage(_media.message);
                }

                _results.data.add(_result);
            } catch (Throwable e) {
                logger.error(e.getMessage(), e);

                AddMediaResult _result = new AddMediaResult();
                _result.setSuccess(false);
                _result.setMessage(e.getMessage());

                _results.data.add(_result);
            } finally {
                if (_file != null) {
                    _file.delete();
                }
            }
        }

        return _results;
    }

    @PostMapping(value = "/remove")
    public Result<Void> removeDocument(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return conversationRepository.removeDocument(_id, loginUtil.getUserId());
    }

}