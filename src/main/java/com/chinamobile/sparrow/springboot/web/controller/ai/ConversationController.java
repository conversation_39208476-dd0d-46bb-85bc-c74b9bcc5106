package com.chinamobile.sparrow.springboot.web.controller.ai;

import com.chinamobile.sparrow.ai.model.llm.Conversation;
import com.chinamobile.sparrow.ai.model.llm.Memory;
import com.chinamobile.sparrow.ai.repository.llm.AbstractMemoryRepository;
import com.chinamobile.sparrow.ai.repository.llm.ConversationRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonObject;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping(value = "llm/conversation")
public class ConversationController {

    protected final ConversationRepository conversationRepository;
    protected final AbstractMemoryRepository memoryRepository;
    protected final LoginUtil loginUtil;

    public ConversationController(ConversationRepository conversationRepository, AbstractMemoryRepository memoryRepository, LoginUtil loginUtil
    ) {
        this.conversationRepository = conversationRepository;
        this.memoryRepository = memoryRepository;
        this.loginUtil = loginUtil;
    }

    @PostMapping(value = "/get")
    public Result<Conversation> get(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return conversationRepository.get(_id);
    }

    @PostMapping(value = "/me")
    public Result<List<Conversation>> me() {
        Result<List<Conversation>> _records = new Result<>();
        _records.data = conversationRepository.me(loginUtil.getUserId());
        return _records;
    }

    @PostMapping(value = "/new")
    public Result<String> newConversation(@RequestBody(required = false) JsonObject data) throws InstantiationException, IllegalAccessException {
        String _originId = data == null ? null : data.get("originId").getAsString();

        return conversationRepository.addOrDefault(_originId, loginUtil.getUserId());
    }

    @PostMapping(value = "/rename")
    public Result<String> rename(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();
        String _title = Optional.ofNullable(data.get("title"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        return conversationRepository.rename(_id, _title, loginUtil.getUserId());
    }

    @PostMapping(value = "/remove")
    public Result<Void> remove(@RequestBody JsonObject data) throws IOException {
        String _id = data.get("id").getAsString();

        return conversationRepository.remove(_id, loginUtil.getUserId());
    }

    @PostMapping(value = "/chat")
    public SseEmitter chat(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();
        String _model = data.get("model").getAsString();
        List<String> _plugins = (List<String>) Optional.ofNullable(data.get("plugins"))
                .map(i -> i.isJsonNull() ? null : ConverterUtil.json2Object(i.toString(), new TypeToken<List<String>>() {
                }.getType())).orElse(null);
        Integer _memorySize = Optional.ofNullable(data.get("memorySize"))
                .map(i -> i.isJsonNull() ? null : i.getAsInt()).orElse(null);
        String _message = data.get("message").getAsString();
        String _mediaId = Optional.ofNullable(data.get("mediaId"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        return conversationRepository.generate(_id, _model, _plugins, _memorySize, _message, _mediaId, loginUtil.getUserId());
    }

    @PostMapping(value = "/memory")
    public Result<List<Memory>> memory(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        Result<List<Memory>> _memories = new Result<>();
        if (conversationRepository.accessible(_id, loginUtil.getUserId())) {
            _memories.data = memoryRepository.find(-1, _id);
        } else {
            _memories.setCode(Result.DATA_ACCESS_DENY);
        }

        return _memories;
    }

    @PostMapping(value = "/memory/clear")
    public Result<Void> clearConversation(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return memoryRepository.remove(_id, loginUtil.getUserId());
    }

}