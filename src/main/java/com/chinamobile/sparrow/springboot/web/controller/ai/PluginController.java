package com.chinamobile.sparrow.springboot.web.controller.ai;

import com.chinamobile.sparrow.ai.service.dify.ConsoleFacade;
import com.chinamobile.sparrow.ai.service.dify.lang.Token;
import com.chinamobile.sparrow.domain.infra.code.Result;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping(value = "llm/plugin")
public class PluginController {

    protected final String speechKey;
    protected final String speechServiceRegion;
    protected final ConsoleFacade consoleFacade;

    public PluginController(String speechKey, String speechServiceRegion, ConsoleFacade consoleFacade) {
        this.speechKey = speechKey;
        this.speechServiceRegion = speechServiceRegion;
        this.consoleFacade = consoleFacade;
    }

    @PostMapping(value = "/speech-key")
    public Result<List<String>> speechKey() {
        Result<List<String>> _speechKey = new Result<>();
        _speechKey.data = Arrays.asList(speechKey, speechServiceRegion);
        return _speechKey;
    }

    @PostMapping(value = "/dify/token")
    public Result<Token> token() {
        Result<Token> _token = new Result<>();
        _token.data = consoleFacade.getToken();
        return _token;
    }

}