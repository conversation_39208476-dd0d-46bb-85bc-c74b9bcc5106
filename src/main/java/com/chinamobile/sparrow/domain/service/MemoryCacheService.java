package com.chinamobile.sparrow.domain.service;

import org.springframework.util.StringUtils;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

public class MemoryCacheService implements ICacheService {

    final ConcurrentHashMap<String, CacheEntry> store;

    public MemoryCacheService() {
        this.store = new ConcurrentHashMap<>();
    }

    @Override
    public Set<String> keys(String pattern) {
        if (!StringUtils.hasLength(pattern) || pattern.equals("*")) {
            return new HashSet<>(store.keySet());
        }

        String _regex = pattern.replace("*", ".*").replace("?", ".");
        Pattern _pattern = Pattern.compile(_regex);

        Set<String> _keys = new HashSet<>();
        for (String i : store.keySet()) {
            if (_pattern.matcher(i).matches()) {
                _keys.add(i);
            }
        }
        return _keys;
    }

    @Override
    public boolean hasKey(String key) {
        return store.containsKey(key);
    }

    @Override
    public void set(String key, Object value, Duration duration) {
        long _expireAt = duration == null ? -1 : (System.currentTimeMillis() + duration.toMillis());
        store.put(key, new CacheEntry(value, _expireAt));
    }

    @Override
    public <T> T get(String key, Class<T> tClass) {
        return Optional.ofNullable(getEntry(key))
                .map(i -> (T) i.value).orElse(null);
    }

    @Override
    public boolean delete(String key) {
        return store.remove(key) != null;
    }

    CacheEntry getEntry(String key) {
        CacheEntry e = store.get(key);
        if (e != null && e.expired()) {
            store.remove(key);
            return null;
        }

        return e;
    }

    static class CacheEntry {

        final Object value;
        final long expireAt; // -1 表示不过期

        CacheEntry(Object value, long expireAt) {
            this.value = value;
            this.expireAt = expireAt;
        }

        boolean expired() {
            return expireAt != -1 && System.currentTimeMillis() > expireAt;
        }
    }

}