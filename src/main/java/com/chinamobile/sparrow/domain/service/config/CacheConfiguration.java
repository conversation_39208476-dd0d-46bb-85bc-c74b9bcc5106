package com.chinamobile.sparrow.domain.service.config;

import com.chinamobile.sparrow.domain.service.ICacheService;
import com.chinamobile.sparrow.domain.service.impl.MemoryCacheService;
import com.chinamobile.sparrow.domain.service.impl.RedisCacheService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * 缓存配置类
 * 支持内存缓存和 Redis 缓存的无缝切换
 * 通过配置属性 cache.type 控制使用哪种缓存实现
 * - memory: 使用内存缓存（默认）
 * - redis: 使用 Redis 缓存
 */
@Configuration
public class CacheConfiguration {

    /**
     * 缓存配置属性
     */
    @ConfigurationProperties(prefix = "cache")
    public static class CacheProperties {
        /**
         * 缓存类型：memory 或 redis
         */
        private String type = "memory";

        /**
         * Redis 配置
         */
        private Redis redis = new Redis();

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public Redis getRedis() {
            return redis;
        }

        public void setRedis(Redis redis) {
            this.redis = redis;
        }

        public static class Redis {
            /**
             * Redis 键前缀
             */
            private String keyPrefix = "sparrow:cache:";

            /**
             * 默认过期时间（秒）
             */
            private long defaultExpiration = 3600;

            public String getKeyPrefix() {
                return keyPrefix;
            }

            public void setKeyPrefix(String keyPrefix) {
                this.keyPrefix = keyPrefix;
            }

            public long getDefaultExpiration() {
                return defaultExpiration;
            }

            public void setDefaultExpiration(long defaultExpiration) {
                this.defaultExpiration = defaultExpiration;
            }
        }
    }

    /**
     * 缓存配置属性 Bean
     */
    @Bean
    @ConfigurationProperties(prefix = "cache")
    public CacheProperties cacheProperties() {
        return new CacheProperties();
    }

    /**
     * Redis 模板配置
     * 只有在使用 Redis 缓存时才创建
     */
    @Bean
    @ConditionalOnProperty(name = "cache.type", havingValue = "redis")
    @ConditionalOnMissingBean(name = "cacheRedisTemplate")
    public RedisTemplate<String, Object> cacheRedisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        
        // 设置键序列化器
        template.setKeySerializer(new StringRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());
        
        // 设置值序列化器
        GenericJackson2JsonRedisSerializer jsonSerializer = new GenericJackson2JsonRedisSerializer();
        template.setValueSerializer(jsonSerializer);
        template.setHashValueSerializer(jsonSerializer);
        
        template.afterPropertiesSet();
        return template;
    }

    /**
     * 内存缓存服务
     * 当 cache.type=memory 或未配置时使用
     */
    @Bean
    @ConditionalOnProperty(name = "cache.type", havingValue = "memory", matchIfMissing = true)
    @Primary
    public ICacheService memoryCacheService() {
        return new MemoryCacheService();
    }

    /**
     * Redis 缓存服务
     * 当 cache.type=redis 时使用
     */
    @Bean
    @ConditionalOnProperty(name = "cache.type", havingValue = "redis")
    @Primary
    public ICacheService redisCacheService(RedisTemplate<String, Object> cacheRedisTemplate) {
        return new RedisCacheService(cacheRedisTemplate);
    }
}
