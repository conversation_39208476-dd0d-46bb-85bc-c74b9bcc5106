package com.chinamobile.sparrow.domain.service.search;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.HttpUtil;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.chinamobile.sparrow.domain.util.HttpUtil.createUnsafeSSLContext;

@Service
public class BingSearchFacade implements IWebSearchService {

    final String baseUrl;
    final String subscriptionKey;
    final OkHttpClient client;

    public BingSearchFacade(@Value("${bing.search.base-url:}") String baseUrl, @Value("${bing.search.api-key:}") String subscriptionKey, ConnectionPool connectionPool) {
        this.baseUrl = baseUrl;
        this.subscriptionKey = subscriptionKey;
        this.client = new OkHttpClient.Builder()
                .connectionPool(connectionPool == null ? new ConnectionPool() : connectionPool)
                .sslSocketFactory(createUnsafeSSLContext().getSocketFactory(), new HttpUtil.TrustAllCertsManager())
                .hostnameVerifier(new HttpUtil.TrustAllHostnameVerifier())
                .build();
    }

    @Override
    public Result<List<Page>> search(String query, int count) {
        HttpUrl _url = HttpUrl.parse(baseUrl + "/search").newBuilder()
                .addQueryParameter("q", query)
                .addQueryParameter("count", String.valueOf(count))
                .build();

        Request _request = new Request.Builder()
                .url(_url)
                .addHeader("Ocp-Apim-Subscription-Key", subscriptionKey)
                .addHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
                .get()
                .build();

        Result<List<Page>> _pages = new Result<>();
        try (Response response = client.newCall(_request).execute()) {
            if (!response.isSuccessful()) {
                _pages.setCode(Result.ENUM_ERROR.N, response.code());
                _pages.message = response.message();
                return _pages;
            }

            if (response.body() == null) {
                _pages.data = new ArrayList<>();
                return _pages;
            }

            String _body = response.body().string();
            JsonObject _json = ConverterUtil.json2Object(_body, JsonObject.class);

            _pages.data = new ArrayList<>();

            _json = Optional.ofNullable(_json.get("webPages"))
                    .map(JsonElement::getAsJsonObject).orElse(null);
            if (_json == null) {
                return _pages;
            }

            _json.getAsJsonArray("value").forEach(i -> {
                JsonObject j = i.getAsJsonObject();

                Page _page = new Page();
                _page.setTitle(j.get("name").getAsString());
                _page.setUrl(j.get("url").getAsString());
                _page.setSnippet(j.get("snippet").getAsString());
                _pages.data.add(_page);
            });

            return _pages;
        } catch (IOException e) {
            _pages.setCode(Result.ENUM_ERROR.N, 1);
            _pages.message = e.getMessage();
            return _pages;
        }
    }

}