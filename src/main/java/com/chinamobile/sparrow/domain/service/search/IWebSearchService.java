package com.chinamobile.sparrow.domain.service.search;

import com.chinamobile.sparrow.domain.infra.code.Result;

import java.util.List;

public interface IWebSearchService {

    Result<List<Page>> search(String query, int count);

    class Page {

        private String title;
        private String url;
        private String snippet;

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getSnippet() {
            return snippet;
        }

        public void setSnippet(String snippet) {
            this.snippet = snippet;
        }

    }

}