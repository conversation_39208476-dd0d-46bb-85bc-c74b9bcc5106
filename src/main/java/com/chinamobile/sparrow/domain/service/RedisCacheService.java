package com.chinamobile.sparrow.domain.service;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.util.Map;
import java.util.Set;

public class RedisCacheService implements ICacheService {

    private final RedisTemplate<String, Object> redisTemplate;

    public RedisCacheService(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Override
    public Set<String> keys(String pattern) {
        return redisTemplate.keys(StringUtils.hasLength(pattern) ? pattern : "*");
    }

    @Override
    public boolean hasKey(String key) {
        return redisTemplate.hasKey(key);
    }

    @Override
    public void set(String key, Object value, Duration duration) {
        if (value == null) {
            redisTemplate.delete(key);
            return;
        }

        if (value instanceof Map) {
            redisTemplate.opsForHash().putAll(key, (Map<?, ?>) value);
        } else if (value instanceof Map.Entry) {
            String _key = String.valueOf(((Map.Entry<?, ?>) value).getKey());
            Object _value = ((Map.Entry<?, ?>) value).getKey();
            redisTemplate.opsForHash().put(key, _key, _value);
        } else if (value instanceof Iterable) {
            redisTemplate.opsForList().rightPushAll(key, value);
        } else {
            redisTemplate.opsForValue().set(key, value);
        }

        redisTemplate.expire(key, duration);
    }

    @Override
    public <T> T get(String key, Class<T> tClass) {
        if (Map.class.isAssignableFrom(tClass)) {
            return (T) redisTemplate.opsForHash().entries(key);
        }
        if (Iterable.class.isAssignableFrom(tClass)) {
            return (T) redisTemplate.opsForList().range(key, 0, -1);
        }

        return (T) redisTemplate.opsForValue().get(key);
    }

    @Override
    public boolean delete(String key) {
        return redisTemplate.delete(key);
    }

}