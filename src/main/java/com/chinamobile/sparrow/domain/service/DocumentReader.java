package com.chinamobile.sparrow.domain.service;

import com.agentsflex.document.parser.PdfBoxDocumentParser;
import com.agentsflex.document.parser.PoiDocumentParser;
import org.apache.commons.io.FilenameUtils;
import org.apache.poi.hssf.extractor.ExcelExtractor;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.xssf.extractor.XSSFExcelExtractor;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.util.FileCopyUtils;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class DocumentReader {

    static final String HTML_SCRIPT_PATTERN = "<script[^>]*?>[\\s\\S]*?<\\/script>";
    static final String HTML_CSS_PATTERN = "<style[^>]*?>[\\s\\S]*?<\\/style>";
    static final String HTML_TAG_PATTERN = "<[^>]+>";
    static final String HTML_CHARACTER_PATTERN = "\\s*|\t|\r|\n"; // 定义空格回车换行符
    static final String HTML_W_PATTERN = "<w[^>]*?>[\\s\\S]*?<\\/w[^>]*?>"; //定义所有w标签

    public static String parse(File file) throws IOException {
        return parse(file.getName(), FileCopyUtils.copyToByteArray(file));
    }

    public static String parse(String name, byte[] bytes) throws IOException {
        String _extension = FilenameUtils.getExtension(name);

        switch (_extension) {
            case "csv":
            case "md":
            case "txt":
                return readText(bytes);
            case "doc":
            case "docx":
                return readWord(bytes);
            case "pdf":
                return readPDF(bytes);
            case "xls":
                return readExcel(bytes);
            case "xlsx":
                return readExcel2007(bytes);
            default:
                throw new UnsupportedOperationException(_extension);
        }
    }

    public static String readExcel(byte[] bytes) throws IOException {
        try (InputStream input = new ByteArrayInputStream(bytes);
             HSSFWorkbook book = new HSSFWorkbook(new POIFSFileSystem(input));
             ExcelExtractor extractor = new ExcelExtractor(book)) {
            extractor.setFormulasNotResults(false);
            extractor.setIncludeSheetNames(false);
            return extractor.getText();
        }
    }

    public static String readExcel2007(byte[] bytes) throws IOException {
        try (InputStream input = new ByteArrayInputStream(bytes);
             XSSFWorkbook book = new XSSFWorkbook(input);
             XSSFExcelExtractor extractor = new XSSFExcelExtractor(book)) {
            extractor.setIncludeSheetNames(false);
            return extractor.getText().replace("null", "");
        }
    }

    public static String readWord(byte[] bytes) {
        return new PoiDocumentParser().parse(new ByteArrayInputStream(bytes)).getContent();
    }

    public static String readPDF(byte[] bytes) throws IOException {
        return new PdfBoxDocumentParser().parse(new ByteArrayInputStream(bytes)).getContent();
    }

    public static String readHtml(byte[] bytes) throws IOException {
        String _encoding = getCharset(bytes);
        String _html = new String(bytes, _encoding);
        return removeHtmlTag(_html);
    }

    public static String readText(byte[] bytes) throws IOException {
        String _encoding = getCharset(bytes);
        return new String(bytes, _encoding);
    }

    static String getCharset(byte[] bytes) throws IOException {
        try (InputStream input = new ByteArrayInputStream(bytes)) {
            input.mark(0);

            byte[] _first3Bytes = new byte[3];
            int _length = input.read(_first3Bytes, 0, 3);
            if (_length == -1) {
                return "GBK";
            }

            if (_first3Bytes[0] == (byte) 0xFF && _first3Bytes[1] == (byte) 0xFE) {
                return "UTF-16LE";
            }

            if (_first3Bytes[0] == (byte) 0xFE && _first3Bytes[1] == (byte) 0xFF) {
                return "UTF-16BE";
            }

            if (_first3Bytes[0] == (byte) 0xEF && _first3Bytes[1] == (byte) 0xBB && _first3Bytes[2] == (byte) 0xBF) {
                return "UTF-8";
            }

            input.reset();

            while ((_length = input.read()) != -1) {
                if (_length >= 0xF0)
                    break;

                // 单独出现BF以下的，也算是GBK
                if (0x80 <= _length && _length <= 0xBF)
                    break;

                // 双字节（0xC0 - 0xDF）
                if (0xC0 <= _length && _length <= 0xDF) {
                    _length = input.read();
                    if (0x80 <= _length && _length <= 0xBF) {
                        // 0x80 - 0xBF，也可能在GBK编码内
                        continue;
                    } else {
                        break;
                    }
                }

                // 也有可能出错，但是几率较小
                if (0xE0 <= _length && _length <= 0xEF) {
                    _length = input.read();
                    if (0x80 <= _length && _length <= 0xBF) {
                        _length = input.read();
                        if (0x80 <= _length && _length <= 0xBF) {
                            return "UTF-8";
                        } else {
                            break;
                        }
                    } else {
                        break;
                    }
                }
            }
        }

        return "GBK";
    }

    static String removeHtmlTag(String html) {
        Pattern _pattern = Pattern.compile(HTML_SCRIPT_PATTERN, Pattern.CASE_INSENSITIVE);
        Matcher _matcher = _pattern.matcher(html);
        html = _matcher.replaceAll("");

        _pattern = Pattern.compile(HTML_CSS_PATTERN, Pattern.CASE_INSENSITIVE);
        _matcher = _pattern.matcher(html);
        html = _matcher.replaceAll("");

        _pattern = Pattern.compile(HTML_TAG_PATTERN, Pattern.CASE_INSENSITIVE);
        _matcher = _pattern.matcher(html);
        html = _matcher.replaceAll("");

        _pattern = Pattern.compile(HTML_CHARACTER_PATTERN, Pattern.CASE_INSENSITIVE);
        _matcher = _pattern.matcher(html);
        html = _matcher.replaceAll("");

        _pattern = Pattern.compile(HTML_W_PATTERN, Pattern.CASE_INSENSITIVE);
        _matcher = _pattern.matcher(html);
        html = _matcher.replaceAll("");

        html = html.replaceAll(" ", "");
        return html.trim();
    }

}