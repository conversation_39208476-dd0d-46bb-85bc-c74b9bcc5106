package com.chinamobile.sparrow.domain.service;

import java.time.Duration;
import java.util.Set;

public interface ICacheService {

    Set<String> keys(String pattern);

    boolean hasKey(String key);

    default void set(String key, Object value) {
        set(key, value, Duration.ofMinutes(30));
    }

    void set(String key, Object value, Duration duration);

    default String get(String key) {
        return get(key, String.class);
    }

    <T> T get(String key, Class<T> tClass);

    boolean delete(String key);

}