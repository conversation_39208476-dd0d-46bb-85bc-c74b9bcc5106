package com.chinamobile.sparrow.domain.repository.sys;

import com.chinamobile.sparrow.ai.model.llm.Conversation;
import com.chinamobile.sparrow.ai.model.llm.Memory;
import com.chinamobile.sparrow.ai.repository.llm.AbstractMemoryRepository;
import com.chinamobile.sparrow.ai.repository.llm.ConversationRepository;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import org.jinq.jpa.JinqJPAStreamProvider;

import javax.persistence.EntityManagerFactory;
import java.util.List;
import java.util.stream.Collectors;

public class ConversationUserRepository<T extends User> extends UserRepository<T> {

    final ConversationRepository<? extends Conversation> conversationRepository;
    final AbstractMemoryRepository<? extends Memory> memoryRepository;

    public ConversationUserRepository(EntityManagerFactory entityManagerFactory, JinqJPAStreamProvider jinqJPAStreamProvider, ConversationRepository<?> conversationRepository, AbstractMemoryRepository<? extends Memory> memoryRepository, AbstractMediaRepository mediaRepository, String passwordConstraint, String rsaPrivateKey, Class<T> tClass) {
        super(entityManagerFactory, jinqJPAStreamProvider, mediaRepository, passwordConstraint, rsaPrivateKey, tClass);
        this.conversationRepository = conversationRepository;
        this.memoryRepository = memoryRepository;
    }

    @Override
    public void logout(String id) {
        // 清理对话
        List<String> _conversationIds = conversationRepository.me(id).stream()
                .map(Conversation::getId)
                .collect(Collectors.toList());

        for (String i : _conversationIds) {
            memoryRepository.clear(i);
        }

        super.logout(id);
    }

}